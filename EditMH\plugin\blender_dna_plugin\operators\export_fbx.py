"""
Export FBX Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to export combined meshes and DNA files
for Unreal Engine import, removing extra bones and coordinate transformations.
"""

import os
import bpy
import time
import traceback
from bpy.props import StringProperty, BoolProperty
from mathutils import Vector, Matrix

# Import the DNA utils
from ..utils.dna_utils import check_dna_modules, ensure_dna_modules_path
from ..utils.ui_utils import update_ui

# Set up logging
def log_info(message):
    """Log an info message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [INFO] {message}")

def log_warning(message):
    """Log a warning message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [WARNING] {message}")

def log_error(message):
    """Log an error message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [ERROR] {message}")

def log_debug(message):
    """Log a debug message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [DEBUG] {message}")


class DNA_OT_ExportFBX(bpy.types.Operator):
    """Export combined meshes and DNA file for Unreal Engine"""
    bl_idname = "dna.export_fbx"
    bl_label = "Export FBX & DNA"
    bl_description = "Export combined meshes and DNA file for Unreal Engine import"
    bl_options = {'REGISTER', 'UNDO'}

    # File browser properties
    filepath: StringProperty(
        name="File Path",
        description="Path to export the files",
        default="//export/",
        subtype='DIR_PATH'
    )

    def invoke(self, context, event):
        """Invoke the file browser"""
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

    def execute(self, context):
        """Execute the export operation"""
        log_info("=== DNA Export FBX Process Started ===")

        # Update status message
        context.scene.dna_tools.status_message = "Exporting FBX and DNA files..."
        update_ui()

        # Check if DNA is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            log_error("No DNA file loaded")
            context.scene.dna_tools.status_message = "Error: No DNA file loaded"
            update_ui()
            self.report({'ERROR'}, "No DNA file loaded. Import a DNA file first.")
            return {'CANCELLED'}

        # Check if mesh has been created
        if not dna_tools.is_mesh_created:
            log_error("No mesh created")
            context.scene.dna_tools.status_message = "Error: No mesh created"
            update_ui()
            self.report({'ERROR'}, "No mesh created. Create a mesh first.")
            return {'CANCELLED'}

        try:
            # Get export directory from filepath
            export_dir = os.path.dirname(self.filepath)
            if not export_dir:
                export_dir = bpy.path.abspath("//export/")

            # Ensure export directory exists
            os.makedirs(export_dir, exist_ok=True)
            log_info(f"Export directory: {export_dir}")

            # Get model name from DNA file
            dna_file_path = dna_tools.dna_file_path
            model_name = os.path.splitext(os.path.basename(dna_file_path))[0]
            log_info(f"Model name: {model_name}")

            # Export DNA file
            log_info("Exporting DNA file...")
            context.scene.dna_tools.status_message = "Exporting DNA file..."
            update_ui()

            dna_export_path = os.path.join(export_dir, f"{model_name}.dna")
            self.export_dna_file(context, dna_export_path)

            # Export FBX file
            log_info("Exporting FBX file...")
            context.scene.dna_tools.status_message = "Exporting FBX file..."
            update_ui()

            fbx_export_path = os.path.join(export_dir, f"{model_name}.fbx")
            export_collection, export_armature = self.export_fbx_file(context, fbx_export_path, model_name)

            # Clean up export collection after successful export
            if export_collection:
                self.cleanup_export_collection(context, export_collection)

            # Restore original armature name
            if export_armature:
                self.restore_armature_name(context, export_armature)

            log_info("Export completed successfully")
            context.scene.dna_tools.status_message = f"Export completed: {export_dir}"
            update_ui()

            self.report({'INFO'}, f"Export completed successfully to: {export_dir}")
            return {'FINISHED'}

        except Exception as e:
            log_error(f"Error during export: {str(e)}")
            log_error(f"Error type: {type(e).__name__}")
            import traceback
            log_error(f"Full traceback: {traceback.format_exc()}")
            context.scene.dna_tools.status_message = f"Error during export: {str(e)}"
            update_ui()
            self.report({'ERROR'}, f"Error during export: {str(e)}")
            return {'CANCELLED'}

    def export_dna_file(self, context, export_path):
        """Export the DNA file with coordinate transformations applied using existing DNA export functionality"""
        log_info(f"Exporting DNA file with coordinate transformations to: {export_path}")

        try:
            # Implement proper DNA export with coordinate transformations
            from ..utils.dna_utils import ensure_dna_modules_path
            ensure_dna_modules_path()

            import dna
            from dna import FileStream, BinaryStreamReader, BinaryStreamWriter, Status, DataLayer_All

            # Get the original DNA file path
            dna_tools = context.scene.dna_tools
            original_dna_path = dna_tools.dna_file_path

            # Read the original DNA file
            log_info("Reading original DNA file...")
            source_stream = FileStream(original_dna_path, FileStream.AccessMode_Read, FileStream.OpenMode_Binary)
            source_reader = BinaryStreamReader(source_stream, DataLayer_All)
            source_reader.read()

            if not Status.isOk():
                raise Exception(f"Error reading DNA file: {Status.get().message}")

            # Create a file stream for writing the target DNA
            target_stream = FileStream(export_path, FileStream.AccessMode_Write, FileStream.OpenMode_Binary)
            target_writer = BinaryStreamWriter(target_stream)

            # Copy all data from the source to the target
            try:
                target_writer.setFrom(source_reader, DataLayer_All, dna.UnknownLayerPolicy_Preserve)
            except AttributeError:
                try:
                    target_writer.setFrom(source_reader, DataLayer_All, dna.UnknownLayerPolicy.Preserve)
                except AttributeError:
                    target_writer.setFrom(source_reader, DataLayer_All)

            # Apply coordinate transformations properly
            log_info("Applying coordinate transformations to DNA data...")
            self.apply_coordinate_transformations_to_dna(context, source_reader, target_writer)

            # Write the target DNA
            target_writer.write()

            if not Status.isOk():
                raise Exception(f"Error writing DNA file: {Status.get().message}")

            log_info("DNA file exported successfully with coordinate transformations")

        except Exception as e:
            log_error(f"Error exporting transformed DNA file: {str(e)}")
            import traceback
            traceback.print_exc()
            raise Exception(f"DNA export failed: {str(e)}")

    def apply_coordinate_transformations_to_dna(self, context, reader, writer):
        """Apply coordinate transformations to DNA data to match FBX export"""
        log_info("Applying coordinate transformations to DNA data...")

        # Import required modules
        from ..utils.rotation_utils import BLENDER_TO_DNA_MATRIX
        from mathutils import Matrix, Vector
        import math

        # Update mesh data if meshes exist
        if context.scene.dna_tools.is_mesh_created:
            self.update_dna_mesh_data(context, reader, writer)
            self.update_dna_blendshape_data(context, reader, writer)

        # Update bone data if armature exists
        if context.scene.dna_tools.is_armature_created:
            self.update_dna_bone_data(context, reader, writer)

    def update_dna_mesh_data(self, context, reader, writer):
        """Update mesh vertex positions in DNA with coordinate transformations"""
        log_info("Updating DNA mesh data...")

        # Get the model name
        dna_file_path = context.scene.dna_tools.dna_file_path
        model_name = os.path.splitext(os.path.basename(dna_file_path))[0]

        # Find mesh objects
        mesh_objects = []
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and obj.name.startswith(model_name):
                mesh_objects.append(obj)

        log_info(f"Found {len(mesh_objects)} mesh objects for DNA update")

        # Process each mesh
        for mesh_obj in mesh_objects:
            # Find corresponding mesh in DNA
            mesh_index = -1
            for i in range(reader.getMeshCount()):
                if reader.getMeshName(i) == mesh_obj.name:
                    mesh_index = i
                    break

            if mesh_index == -1:
                raise Exception(f"Mesh not found in DNA: {mesh_obj.name}")

            log_info(f"Updating DNA mesh: {mesh_obj.name}")

            # Get vertex positions and apply transformations
            from ..utils.rotation_utils import BLENDER_TO_DNA_MATRIX
            from mathutils import Vector

            vertex_positions = []
            mesh = mesh_obj.data
            world_matrix = mesh_obj.matrix_world.copy()

            for v in mesh.vertices:
                # Transform to world space
                world_pos = world_matrix @ Vector(v.co)
                # Transform to DNA space
                dna_pos = BLENDER_TO_DNA_MATRIX @ world_pos
                # Keep in Blender units (no scale conversion)
                vertex_positions.append([dna_pos.x, dna_pos.y, dna_pos.z])

            # Update DNA
            writer.setVertexPositions(mesh_index, vertex_positions)
            log_info(f"Updated {len(vertex_positions)} vertices for mesh {mesh_obj.name}")

    def update_dna_blendshape_data(self, context, reader, writer):
        """Update blendshape data in DNA with coordinate transformations"""
        log_info("Updating DNA blendshape data...")

        # Get the model name
        dna_file_path = context.scene.dna_tools.dna_file_path
        model_name = os.path.splitext(os.path.basename(dna_file_path))[0]

        # Find mesh objects with shape keys
        mesh_objects = []
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and obj.name.startswith(model_name) and obj.data.shape_keys:
                mesh_objects.append(obj)

        log_info(f"Found {len(mesh_objects)} mesh objects with shape keys for DNA update")

        # Process each mesh
        for mesh_obj in mesh_objects:
            # Find corresponding mesh in DNA
            mesh_index = -1
            for i in range(reader.getMeshCount()):
                if reader.getMeshName(i) == mesh_obj.name:
                    mesh_index = i
                    break

            if mesh_index == -1:
                raise Exception(f"Mesh not found in DNA for blendshapes: {mesh_obj.name}")

            # Process blendshape targets
            blendshape_target_count = reader.getBlendShapeTargetCount(mesh_index)
            if blendshape_target_count == 0:
                log_info(f"No blendshape targets found for mesh: {mesh_obj.name}")
                continue

            log_info(f"Updating DNA blendshapes for mesh: {mesh_obj.name}")

            shape_keys = mesh_obj.data.shape_keys.key_blocks
            world_matrix = mesh_obj.matrix_world.copy()

            # Process each blendshape target
            for target_index in range(blendshape_target_count):
                try:
                    channel_index = reader.getBlendShapeChannelIndex(mesh_index, target_index)
                    channel_name = reader.getBlendShapeChannelName(channel_index)

                    # Find corresponding shape key
                    shape_key = None
                    for key in shape_keys:
                        if key.name == channel_name or key.name.endswith(f"__{channel_name}"):
                            shape_key = key
                            break

                    if not shape_key:
                        raise Exception(f"Shape key not found for blendshape: {channel_name}")

                    # Get basis shape key
                    basis_key = shape_keys[0] if shape_keys[0].name == "Basis" else shape_keys[0]

                    # Get vertex indices
                    vertex_indices = list(reader.getBlendShapeTargetVertexIndices(mesh_index, target_index))

                    # Calculate deltas
                    from ..utils.rotation_utils import BLENDER_TO_DNA_MATRIX
                    from mathutils import Vector

                    delta_xs, delta_ys, delta_zs = [], [], []

                    for vertex_index in vertex_indices:
                        if vertex_index < len(shape_key.data):
                            # Get positions
                            basis_pos = Vector(basis_key.data[vertex_index].co)
                            shape_pos = Vector(shape_key.data[vertex_index].co)

                            # Calculate delta
                            local_delta = shape_pos - basis_pos
                            world_delta = world_matrix.to_3x3() @ local_delta
                            dna_delta = BLENDER_TO_DNA_MATRIX.to_3x3() @ world_delta

                            # Keep in Blender units (no scale conversion)
                            delta_xs.append(dna_delta.x)
                            delta_ys.append(dna_delta.y)
                            delta_zs.append(dna_delta.z)

                    # Update DNA
                    writer.setBlendShapeTargetDeltas(mesh_index, target_index, delta_xs, delta_ys, delta_zs)
                    log_info(f"Updated blendshape {channel_name} with {len(delta_xs)} deltas")

                except Exception as e:
                    log_error(f"Error updating blendshape target {target_index}: {str(e)}")

    def update_dna_bone_data(self, context, reader, writer):
        """Update bone data in DNA with coordinate transformations"""
        log_info("Updating DNA bone data...")

        # Find armature
        armature_obj = None
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE':
                armature_obj = obj
                break

        if not armature_obj:
            log_warning("No armature found for DNA bone update")
            return

        # Get joint count
        joint_count = reader.getJointCount()
        log_info(f"Updating {joint_count} joints in DNA")

        # Store current mode and selection
        original_mode = bpy.context.mode
        original_active = bpy.context.active_object

        try:
            # Enter edit mode
            bpy.ops.object.mode_set(mode='OBJECT')
            bpy.ops.object.select_all(action='DESELECT')
            armature_obj.select_set(True)
            bpy.context.view_layer.objects.active = armature_obj
            bpy.ops.object.mode_set(mode='EDIT')

            # Apply global transformation matrix (following example implementation)
            from mathutils import Matrix
            import math
            rotation_x = Matrix.Rotation(math.radians(-90), 4, 'X')
            global_matrix = rotation_x.to_4x4()

            edit_bones = armature_obj.data.edit_bones

            # Create bone lookup
            edit_bone_lookup = {}
            for edit_bone in edit_bones:
                edit_bone_lookup[edit_bone.name] = edit_bone

            # Process each joint
            joint_translations = []
            joint_rotations_x = []
            joint_rotations_y = []
            joint_rotations_z = []

            for joint_index in range(joint_count):
                joint_name = reader.getJointName(joint_index)
                edit_bone = edit_bone_lookup.get(joint_name)

                if not edit_bone:
                    raise Exception(f"Edit bone not found for joint: {joint_name}")

                # Apply transformations (following example implementation)
                if joint_index == 0:  # Root bone
                    translation, rotation, _ = (global_matrix @ edit_bone.matrix).decompose()
                else:
                    # Child bone - relative to parent
                    if edit_bone.parent:
                        local_matrix = edit_bone.parent.matrix.inverted() @ edit_bone.matrix
                        translation, rotation, _ = local_matrix.decompose()
                    else:
                        translation, rotation, _ = (global_matrix @ edit_bone.matrix).decompose()

                # Keep in Blender units (no scale conversion)
                joint_translations.append([translation.x, translation.y, translation.z])

                # Convert rotation to degrees
                euler = rotation.to_euler('XYZ')
                joint_rotations_x.append(math.degrees(euler.x))
                joint_rotations_y.append(math.degrees(euler.y))
                joint_rotations_z.append(math.degrees(euler.z))

            # Update DNA
            writer.setNeutralJointTranslations(joint_translations)
            writer.setNeutralJointRotationXs(joint_rotations_x)
            writer.setNeutralJointRotationYs(joint_rotations_y)
            writer.setNeutralJointRotationZs(joint_rotations_z)

            log_info(f"Updated {joint_count} joint transforms in DNA")

        finally:
            # Restore mode and selection
            bpy.ops.object.mode_set(mode='OBJECT')
            if original_active:
                bpy.context.view_layer.objects.active = original_active
            if original_mode != 'OBJECT':
                try:
                    bpy.ops.object.mode_set(mode=original_mode)
                except:
                    pass

    def export_fbx_file(self, context, export_path, model_name):
        """Export the FBX file with combined meshes and clean armature"""
        log_info(f"Exporting FBX file to: {export_path}")

        # Store original selection and active object
        original_selection = context.selected_objects.copy()
        original_active = context.active_object
        original_mode = context.mode

        try:
            # Ensure we're in object mode
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Find the model collection and all mesh objects
            model_collection = None
            mesh_objects = []
            armature_obj = None

            # First, try to find the main model collection
            for collection in bpy.data.collections:
                if collection.name == model_name:
                    model_collection = collection
                    break

            if not model_collection:
                raise Exception(f"Model collection '{model_name}' not found")

            # Get mesh objects from the main collection and all sub-collections (LOD collections)
            def collect_meshes_from_collection(collection):
                """Recursively collect mesh objects from a collection and its children"""
                meshes = []

                # Get meshes from this collection
                for obj in collection.objects:
                    if obj.type == 'MESH':
                        meshes.append(obj)

                # Get meshes from child collections (LOD collections)
                for child_collection in collection.children:
                    meshes.extend(collect_meshes_from_collection(child_collection))

                return meshes

            # Collect all mesh objects from the model collection and its children
            mesh_objects = collect_meshes_from_collection(model_collection)
            log_info(f"Found {len(mesh_objects)} mesh objects to export")

            if not mesh_objects:
                # Try to find meshes in any collection that starts with the model name
                log_warning(f"No meshes found in main collection, searching in collections starting with '{model_name}'")
                for collection in bpy.data.collections:
                    if collection.name.startswith(model_name):
                        collection_meshes = [obj for obj in collection.objects if obj.type == 'MESH']
                        mesh_objects.extend(collection_meshes)
                        log_info(f"Found {len(collection_meshes)} meshes in collection '{collection.name}'")

                if not mesh_objects:
                    raise Exception(f"No mesh objects found in any collection related to '{model_name}'")

            # Get armature object from the main collection or its children
            def find_armature_in_collection(collection):
                """Recursively find armature in a collection and its children"""
                # Check this collection
                for obj in collection.objects:
                    if obj.type == 'ARMATURE':
                        return obj

                # Check child collections
                for child_collection in collection.children:
                    armature = find_armature_in_collection(child_collection)
                    if armature:
                        return armature

                return None

            armature_obj = find_armature_in_collection(model_collection)

            if not armature_obj:
                # Try to find armature in any collection that starts with the model name
                log_warning(f"No armature found in main collection, searching in collections starting with '{model_name}'")
                for collection in bpy.data.collections:
                    if collection.name.startswith(model_name):
                        for obj in collection.objects:
                            if obj.type == 'ARMATURE':
                                armature_obj = obj
                                break
                        if armature_obj:
                            break

                if not armature_obj:
                    raise Exception(f"No armature found in any collection related to '{model_name}'")

            # Create export collection to keep things organized
            export_collection = self.create_export_collection(context, model_name)

            # Use the existing armature and rename it to 'root' for export
            export_armature = self.prepare_armature_for_export(context, armature_obj, export_collection)

            # Combine all meshes into a single mesh
            combined_mesh = self.combine_meshes(context, mesh_objects, model_name, export_collection)

            # Check if armature modifier already exists
            existing_armature_modifier = None
            for modifier in combined_mesh.modifiers:
                if modifier.type == 'ARMATURE':
                    existing_armature_modifier = modifier
                    break

            if existing_armature_modifier:
                # Update existing modifier to point to export armature
                existing_armature_modifier.object = export_armature
                log_info(f"Updated existing armature modifier: {existing_armature_modifier.name}")
            else:
                # Add new armature modifier
                armature_modifier = combined_mesh.modifiers.new(name="Armature", type='ARMATURE')
                armature_modifier.object = export_armature
                log_info("Added new armature modifier to combined mesh")

            # Ensure we're in object mode before selection operations
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Clear selection manually instead of using select_all
            for obj in context.selected_objects:
                obj.select_set(False)

            # Select objects for export
            export_armature.select_set(True)
            combined_mesh.select_set(True)
            context.view_layer.objects.active = export_armature

            # Export FBX
            log_info("Exporting FBX file...")
            log_info(f"Selected objects for export: {[obj.name for obj in context.selected_objects]}")
            log_info(f"Active object: {context.view_layer.objects.active.name if context.view_layer.objects.active else 'None'}")

            try:
                bpy.ops.export_scene.fbx(
                    filepath=export_path,
                    use_selection=True,
                    object_types={'ARMATURE', 'MESH'},
                    use_mesh_modifiers=True,
                    mesh_smooth_type='FACE',
                    use_armature_deform_only=True,
                    add_leaf_bones=False,
                    primary_bone_axis='Y',
                    secondary_bone_axis='X',
                    armature_nodetype='NULL',
                    bake_anim=False,
                    global_scale=1.0,
                    apply_unit_scale=True,
                    apply_scale_options='FBX_SCALE_NONE'
                )

                # Check if FBX file was actually created
                if os.path.exists(export_path):
                    file_size = os.path.getsize(export_path)
                    log_info(f"FBX file exported successfully: {export_path} ({file_size} bytes)")
                else:
                    raise Exception(f"FBX file was not created at: {export_path}")

            except Exception as fbx_error:
                log_error(f"FBX export failed: {str(fbx_error)}")
                raise Exception(f"FBX export failed: {str(fbx_error)}")

            # Return both the export collection and armature for cleanup by caller
            return export_collection, export_armature

        finally:
            # Restore original selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            for obj in original_selection:
                if obj.name in bpy.data.objects:
                    obj.select_set(True)

            if original_active and original_active.name in bpy.data.objects:
                context.view_layer.objects.active = original_active

            # Restore original mode if it was different
            if original_mode != 'OBJECT' and context.mode == 'OBJECT':
                try:
                    bpy.ops.object.mode_set(mode=original_mode)
                except:
                    log_warning(f"Could not restore original mode: {original_mode}")

    def create_export_collection(self, context, model_name):
        """Create a temporary collection for export objects"""
        export_collection_name = f"{model_name}_Export_Temp"

        # Remove existing export collection if it exists
        if export_collection_name in bpy.data.collections:
            old_collection = bpy.data.collections[export_collection_name]
            bpy.data.collections.remove(old_collection)

        # Create new export collection
        export_collection = bpy.data.collections.new(export_collection_name)
        context.scene.collection.children.link(export_collection)

        log_info(f"Created export collection: {export_collection_name}")
        return export_collection

    def cleanup_export_collection(self, context, export_collection):
        """Clean up the temporary export collection and its objects"""
        if export_collection and export_collection.name in bpy.data.collections:
            # Remove all objects from the collection
            for obj in export_collection.objects:
                bpy.data.objects.remove(obj, do_unlink=True)

            # Remove the collection itself
            bpy.data.collections.remove(export_collection)
            log_info("Cleaned up export collection")

    def prepare_armature_for_export(self, context, original_armature, export_collection):
        """Prepare the existing armature for export by renaming it to 'root'"""
        log_info("Preparing existing armature for export...")

        # Store original name for restoration later
        original_name = original_armature.name
        original_data_name = original_armature.data.name

        # Rename armature to 'root' for export
        original_armature.name = "root"
        original_armature.data.name = "root"

        log_info(f"Renamed armature from '{original_name}' to 'root' for export")
        log_info(f"Armature already has proper bone structure (pelvis → spine_03 → DNA bones)")

        # Store original names for cleanup
        self._original_armature_name = original_name
        self._original_armature_data_name = original_data_name

        return original_armature

    def restore_armature_name(self, context, armature_obj):
        """Restore the original armature name after export"""
        if hasattr(self, '_original_armature_name') and hasattr(self, '_original_armature_data_name'):
            log_info(f"Restoring armature name from 'root' to '{self._original_armature_name}'")
            armature_obj.name = self._original_armature_name
            armature_obj.data.name = self._original_armature_data_name

            # Clean up stored names
            delattr(self, '_original_armature_name')
            delattr(self, '_original_armature_data_name')
        else:
            log_warning("No original armature names stored for restoration")

    def combine_meshes(self, context, mesh_objects, model_name, export_collection):
        """Combine all mesh objects into a single mesh while preserving shape keys"""
        log_info(f"Combining {len(mesh_objects)} mesh objects...")

        if not mesh_objects:
            raise Exception("No mesh objects to combine")

        if len(mesh_objects) == 1:
            # If only one mesh, just create a copy
            original_mesh = mesh_objects[0]
            combined_mesh = original_mesh.copy()
            combined_mesh.data = original_mesh.data.copy()
            combined_mesh.name = f"{model_name}_combined"
            combined_mesh.data.name = f"{model_name}_combined"
            export_collection.objects.link(combined_mesh)
            log_info("Single mesh copied for export")
            return combined_mesh

        # Store original selection
        original_selection = context.selected_objects.copy()
        original_active = context.active_object

        try:
            # Ensure we're in object mode
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Clear selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            # Select all mesh objects
            for obj in mesh_objects:
                obj.select_set(True)

            # Set the first mesh as active
            context.view_layer.objects.active = mesh_objects[0]

            # Duplicate the objects to avoid modifying originals
            bpy.ops.object.duplicate()
            duplicated_objects = context.selected_objects.copy()

            # Move duplicated objects to export collection
            for obj in duplicated_objects:
                # Remove from current collections
                for collection in obj.users_collection:
                    collection.objects.unlink(obj)
                # Add to export collection
                export_collection.objects.link(obj)

            # Join all duplicated meshes
            if len(duplicated_objects) > 1:
                bpy.ops.object.join()

            # Get the combined mesh
            combined_mesh = context.active_object
            combined_mesh.name = f"{model_name}_combined"
            combined_mesh.data.name = f"{model_name}_combined"

            # Ensure shape keys are preserved
            if combined_mesh.data.shape_keys:
                log_info(f"Combined mesh has {len(combined_mesh.data.shape_keys.key_blocks)} shape keys")

                # Ensure Basis shape key exists and is first
                shape_keys = combined_mesh.data.shape_keys.key_blocks
                if shape_keys and shape_keys[0].name != "Basis":
                    log_warning("First shape key is not 'Basis', this might cause issues")

            log_info(f"Meshes combined successfully: {combined_mesh.name}")
            return combined_mesh

        except Exception as e:
            log_error(f"Error combining meshes: {str(e)}")
            raise
        finally:
            # Restore original selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            for obj in original_selection:
                if obj.name in bpy.data.objects:
                    obj.select_set(True)

            if original_active and original_active.name in bpy.data.objects:
                context.view_layer.objects.active = original_active


# Classes to register
classes = [
    DNA_OT_ExportFBX,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
