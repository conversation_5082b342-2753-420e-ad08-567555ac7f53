"""
Export FBX Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to export combined meshes and DNA files
for Unreal Engine import, removing extra bones and coordinate transformations.
"""

import os
import bpy
import time
import traceback
from bpy.props import StringProperty, BoolProperty
from mathutils import Vector, Matrix

# Import the DNA utils
from ..utils.dna_utils import check_dna_modules, ensure_dna_modules_path
from ..utils.ui_utils import update_ui

# Set up logging
def log_info(message):
    """Log an info message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [INFO] {message}")

def log_warning(message):
    """Log a warning message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [WARNING] {message}")

def log_error(message):
    """Log an error message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [ERROR] {message}")

def log_debug(message):
    """Log a debug message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [DEBUG] {message}")


class DNA_OT_ExportFBX(bpy.types.Operator):
    """Export combined meshes and DNA file for Unreal Engine"""
    bl_idname = "dna.export_fbx"
    bl_label = "Export FBX & DNA"
    bl_description = "Export combined meshes and DNA file for Unreal Engine import"
    bl_options = {'REGISTER', 'UNDO'}

    # File browser properties
    filepath: StringProperty(
        name="File Path",
        description="Path to export the files",
        default="//export/",
        subtype='DIR_PATH'
    )

    def invoke(self, context, event):
        """Invoke the file browser"""
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

    def execute(self, context):
        """Execute the export operation"""
        log_info("=== DNA Export FBX Process Started ===")

        # Update status message
        context.scene.dna_tools.status_message = "Exporting FBX and DNA files..."
        update_ui()

        # Check if DNA is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            log_error("No DNA file loaded")
            context.scene.dna_tools.status_message = "Error: No DNA file loaded"
            update_ui()
            self.report({'ERROR'}, "No DNA file loaded. Import a DNA file first.")
            return {'CANCELLED'}

        # Check if mesh has been created
        if not dna_tools.is_mesh_created:
            log_error("No mesh created")
            context.scene.dna_tools.status_message = "Error: No mesh created"
            update_ui()
            self.report({'ERROR'}, "No mesh created. Create a mesh first.")
            return {'CANCELLED'}

        try:
            # Get export directory from filepath
            export_dir = os.path.dirname(self.filepath)
            if not export_dir:
                export_dir = bpy.path.abspath("//export/")

            # Ensure export directory exists
            os.makedirs(export_dir, exist_ok=True)
            log_info(f"Export directory: {export_dir}")

            # Get model name from DNA file
            dna_file_path = dna_tools.dna_file_path
            model_name = os.path.splitext(os.path.basename(dna_file_path))[0]
            log_info(f"Model name: {model_name}")

            # Export DNA file
            log_info("Exporting DNA file...")
            context.scene.dna_tools.status_message = "Exporting DNA file..."
            update_ui()

            dna_export_path = os.path.join(export_dir, f"{model_name}.dna")
            self.export_dna_file(context, dna_export_path)

            # Export FBX file
            log_info("Exporting FBX file...")
            context.scene.dna_tools.status_message = "Exporting FBX file..."
            update_ui()

            fbx_export_path = os.path.join(export_dir, f"{model_name}.fbx")
            self.export_fbx_file(context, fbx_export_path, model_name)

            log_info("Export completed successfully")
            context.scene.dna_tools.status_message = f"Export completed: {export_dir}"
            update_ui()

            self.report({'INFO'}, f"Export completed successfully to: {export_dir}")
            return {'FINISHED'}

        except Exception as e:
            log_error(f"Error during export: {str(e)}")
            traceback.print_exc()
            context.scene.dna_tools.status_message = f"Error during export: {str(e)}"
            update_ui()
            self.report({'ERROR'}, f"Error during export: {str(e)}")
            return {'CANCELLED'}

    def export_dna_file(self, context, export_path):
        """Export the DNA file to the specified path"""
        log_info(f"Exporting DNA file to: {export_path}")

        # Simply copy the original DNA file
        import shutil
        dna_tools = context.scene.dna_tools
        original_dna_path = dna_tools.dna_file_path

        shutil.copy2(original_dna_path, export_path)
        log_info("DNA file exported successfully")

    def export_fbx_file(self, context, export_path, model_name):
        """Export the FBX file with combined meshes and clean armature"""
        log_info(f"Exporting FBX file to: {export_path}")

        # Store original selection and active object
        original_selection = context.selected_objects.copy()
        original_active = context.active_object
        original_mode = context.mode

        try:
            # Ensure we're in object mode
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Find the model collection and all mesh objects
            model_collection = None
            mesh_objects = []
            armature_obj = None

            # First, try to find the main model collection
            for collection in bpy.data.collections:
                if collection.name == model_name:
                    model_collection = collection
                    break

            if not model_collection:
                raise Exception(f"Model collection '{model_name}' not found")

            # Get mesh objects from the main collection and all sub-collections (LOD collections)
            def collect_meshes_from_collection(collection):
                """Recursively collect mesh objects from a collection and its children"""
                meshes = []

                # Get meshes from this collection
                for obj in collection.objects:
                    if obj.type == 'MESH':
                        meshes.append(obj)

                # Get meshes from child collections (LOD collections)
                for child_collection in collection.children:
                    meshes.extend(collect_meshes_from_collection(child_collection))

                return meshes

            # Collect all mesh objects from the model collection and its children
            mesh_objects = collect_meshes_from_collection(model_collection)
            log_info(f"Found {len(mesh_objects)} mesh objects to export")

            if not mesh_objects:
                # Try to find meshes in any collection that starts with the model name
                log_warning(f"No meshes found in main collection, searching in collections starting with '{model_name}'")
                for collection in bpy.data.collections:
                    if collection.name.startswith(model_name):
                        collection_meshes = [obj for obj in collection.objects if obj.type == 'MESH']
                        mesh_objects.extend(collection_meshes)
                        log_info(f"Found {len(collection_meshes)} meshes in collection '{collection.name}'")

                if not mesh_objects:
                    raise Exception(f"No mesh objects found in any collection related to '{model_name}'")

            # Get armature object from the main collection or its children
            def find_armature_in_collection(collection):
                """Recursively find armature in a collection and its children"""
                # Check this collection
                for obj in collection.objects:
                    if obj.type == 'ARMATURE':
                        return obj

                # Check child collections
                for child_collection in collection.children:
                    armature = find_armature_in_collection(child_collection)
                    if armature:
                        return armature

                return None

            armature_obj = find_armature_in_collection(model_collection)

            if not armature_obj:
                # Try to find armature in any collection that starts with the model name
                log_warning(f"No armature found in main collection, searching in collections starting with '{model_name}'")
                for collection in bpy.data.collections:
                    if collection.name.startswith(model_name):
                        for obj in collection.objects:
                            if obj.type == 'ARMATURE':
                                armature_obj = obj
                                break
                        if armature_obj:
                            break

                if not armature_obj:
                    raise Exception(f"No armature found in any collection related to '{model_name}'")

            # Create export collection to keep things organized
            export_collection = self.create_export_collection(context, model_name)

            # Create a copy of the armature with extra bones for export
            export_armature = self.create_clean_armature(context, armature_obj, model_name, export_collection)

            # Combine all meshes into a single mesh
            combined_mesh = self.combine_meshes(context, mesh_objects, model_name, export_collection)

            # Add armature modifier to the combined mesh (no parent relationship needed)
            armature_modifier = combined_mesh.modifiers.new(name="Armature", type='ARMATURE')
            armature_modifier.object = export_armature

            # Ensure we're in object mode before selection operations
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Clear selection manually instead of using select_all
            for obj in context.selected_objects:
                obj.select_set(False)

            # Select objects for export
            export_armature.select_set(True)
            combined_mesh.select_set(True)
            context.view_layer.objects.active = export_armature

            # Export FBX
            log_info("Exporting FBX file...")
            bpy.ops.export_scene.fbx(
                filepath=export_path,
                use_selection=True,
                object_types={'ARMATURE', 'MESH'},
                use_mesh_modifiers=True,
                mesh_smooth_type='FACE',
                use_armature_deform_only=True,
                add_leaf_bones=False,
                primary_bone_axis='Y',
                secondary_bone_axis='X',
                armature_nodetype='NULL',
                bake_anim=False,
                global_scale=1.0,
                apply_unit_scale=True,
                apply_scale_options='FBX_SCALE_NONE'
            )

            log_info("FBX file exported successfully")

            # Clean up export collection
            self.cleanup_export_collection(context, export_collection)

        finally:
            # Restore original selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            for obj in original_selection:
                if obj.name in bpy.data.objects:
                    obj.select_set(True)

            if original_active and original_active.name in bpy.data.objects:
                context.view_layer.objects.active = original_active

            # Restore original mode if it was different
            if original_mode != 'OBJECT' and context.mode == 'OBJECT':
                try:
                    bpy.ops.object.mode_set(mode=original_mode)
                except:
                    log_warning(f"Could not restore original mode: {original_mode}")

    def create_export_collection(self, context, model_name):
        """Create a temporary collection for export objects"""
        export_collection_name = f"{model_name}_Export_Temp"

        # Remove existing export collection if it exists
        if export_collection_name in bpy.data.collections:
            old_collection = bpy.data.collections[export_collection_name]
            bpy.data.collections.remove(old_collection)

        # Create new export collection
        export_collection = bpy.data.collections.new(export_collection_name)
        context.scene.collection.children.link(export_collection)

        log_info(f"Created export collection: {export_collection_name}")
        return export_collection

    def cleanup_export_collection(self, context, export_collection):
        """Clean up the temporary export collection and its objects"""
        if export_collection and export_collection.name in bpy.data.collections:
            # Remove all objects from the collection
            for obj in export_collection.objects:
                bpy.data.objects.remove(obj, do_unlink=True)

            # Remove the collection itself
            bpy.data.collections.remove(export_collection)
            log_info("Cleaned up export collection")

    def create_clean_armature(self, context, original_armature, model_name, export_collection):
        """Create a clean copy of the armature matching MetaHuman reference structure"""
        log_info("Creating export armature matching MetaHuman reference structure...")

        # Create a copy of the armature
        armature_copy = original_armature.copy()
        armature_copy.data = original_armature.data.copy()
        # Use a generic name that won't become an extra bone in Unreal
        armature_copy.name = "Armature"
        armature_copy.data.name = "Armature"

        # Add to export collection
        export_collection.objects.link(armature_copy)

        # Reset transformations (remove coordinate system conversions)
        armature_copy.location = (0, 0, 0)
        armature_copy.rotation_euler = (0, 0, 0)
        armature_copy.scale = (1, 1, 1)

        # Enter edit mode to fix bone hierarchy and reorder bones
        context.view_layer.objects.active = armature_copy
        bpy.ops.object.mode_set(mode='EDIT')

        # Apply bone reordering logic like the example implementation
        # This ensures the bone order matches the reference MetaHuman structure
        log_info("Applying bone reordering to match reference MetaHuman structure...")

        # Get list of extra bones to remove (like the example does)
        from ..constants.metahuman_constants import EXTRA_BONES
        extra_bone_names = [bone_name for bone_name, _ in EXTRA_BONES]

        # First pass: collect all children that need reparenting
        reparenting_info = []
        bones_to_remove = []

        for bone_name in extra_bone_names:
            if bone_name in armature_copy.data.edit_bones:
                bone = armature_copy.data.edit_bones[bone_name]

                # Collect children info before modifying anything
                for child in bone.children:
                    reparenting_info.append(child.name)

                bones_to_remove.append(bone_name)

        # Second pass: reparent children
        for child_name in reparenting_info:
            if child_name in armature_copy.data.edit_bones:
                child_bone = armature_copy.data.edit_bones[child_name]
                child_bone.parent = None
                log_info(f"Reparented {child_name} to be a root bone")

        # Third pass: remove the extra bones
        for bone_name in bones_to_remove:
            if bone_name in armature_copy.data.edit_bones:
                bone = armature_copy.data.edit_bones[bone_name]
                armature_copy.data.edit_bones.remove(bone)
                log_info(f"Removed extra bone: {bone_name}")

        # Exit edit mode
        bpy.ops.object.mode_set(mode='OBJECT')

        log_info(f"Export armature created with {len(armature_copy.data.bones)} bones (matching reference structure)")
        return armature_copy

    def combine_meshes(self, context, mesh_objects, model_name, export_collection):
        """Combine all mesh objects into a single mesh while preserving shape keys"""
        log_info(f"Combining {len(mesh_objects)} mesh objects...")

        if not mesh_objects:
            raise Exception("No mesh objects to combine")

        if len(mesh_objects) == 1:
            # If only one mesh, just create a copy
            original_mesh = mesh_objects[0]
            combined_mesh = original_mesh.copy()
            combined_mesh.data = original_mesh.data.copy()
            combined_mesh.name = f"{model_name}_combined"
            combined_mesh.data.name = f"{model_name}_combined"
            export_collection.objects.link(combined_mesh)
            log_info("Single mesh copied for export")
            return combined_mesh

        # Store original selection
        original_selection = context.selected_objects.copy()
        original_active = context.active_object

        try:
            # Ensure we're in object mode
            if context.mode != 'OBJECT':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Clear selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            # Select all mesh objects
            for obj in mesh_objects:
                obj.select_set(True)

            # Set the first mesh as active
            context.view_layer.objects.active = mesh_objects[0]

            # Duplicate the objects to avoid modifying originals
            bpy.ops.object.duplicate()
            duplicated_objects = context.selected_objects.copy()

            # Join all duplicated meshes
            if len(duplicated_objects) > 1:
                bpy.ops.object.join()

            # Get the combined mesh
            combined_mesh = context.active_object
            combined_mesh.name = f"{model_name}_combined"
            combined_mesh.data.name = f"{model_name}_combined"

            # Ensure shape keys are preserved
            if combined_mesh.data.shape_keys:
                log_info(f"Combined mesh has {len(combined_mesh.data.shape_keys.key_blocks)} shape keys")

                # Ensure Basis shape key exists and is first
                shape_keys = combined_mesh.data.shape_keys.key_blocks
                if shape_keys and shape_keys[0].name != "Basis":
                    log_warning("First shape key is not 'Basis', this might cause issues")

            log_info(f"Meshes combined successfully: {combined_mesh.name}")
            return combined_mesh

        except Exception as e:
            log_error(f"Error combining meshes: {str(e)}")
            raise
        finally:
            # Restore original selection manually
            for obj in context.selected_objects:
                obj.select_set(False)

            for obj in original_selection:
                if obj.name in bpy.data.objects:
                    obj.select_set(True)

            if original_active and original_active.name in bpy.data.objects:
                context.view_layer.objects.active = original_active


# Classes to register
classes = [
    DNA_OT_ExportFBX,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
