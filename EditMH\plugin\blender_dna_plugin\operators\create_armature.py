"""
Create Armature Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to create an armature from a DNA file,
including bone creation, parent-child relationships, and skinning.
"""

import os
import sys
import bpy
import math
import time
import traceback
from bpy.props import String<PERSON>roperty, <PERSON><PERSON><PERSON>roperty, EnumProperty, IntProperty
from mathutils import Vector, Matrix, Euler, Quaternion

# Import the DNA utils
from ..utils.dna_utils import check_dna_modules, ensure_dna_modules_path
from ..utils.ui_utils import update_ui
from ..utils.rotation_utils import dna_to_blender_coords, apply_dna_to_blender_rotation
from ..constants import EXTRA_BONES, CUSTOM_BONE_SHAPE_NAME, CUSTOM_BONE_SHAPE_SCALE, BoneCollection

# Set up logging
def log_info(message):
    """Log an info message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [INFO] {message}")

def log_warning(message):
    """Log a warning message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [WARNING] {message}")

def log_error(message):
    """Log an error message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [ERROR] {message}")

def log_debug(message):
    """Log a debug message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [DEBUG] {message}")

# These functions are now imported from rotation_utils.py

def apply_transforms(obj, location=False, rotation=False, scale=False, recursive=False):
    """Apply transforms to an object

    Args:
        obj: Object to apply transforms to
        location: Whether to apply location
        rotation: Whether to apply rotation
        scale: Whether to apply scale
        recursive: Whether to apply transforms to children recursively
    """
    # Deselect all objects first
    for scene_object in bpy.data.objects:
        scene_object.select_set(False)

    # Make sure we're in object mode
    if bpy.context.mode != 'OBJECT':
        bpy.ops.object.mode_set(mode='OBJECT')

    # Select only the target object
    obj.select_set(True)
    bpy.context.view_layer.objects.active = obj

    # Apply the transforms
    if location and rotation and scale:
        bpy.ops.object.transform_apply(location=True, rotation=True, scale=True)
    else:
        bpy.ops.object.transform_apply(location=location, rotation=rotation, scale=scale)

    # Apply to children if recursive is True
    if recursive:
        for child_object in obj.children:
            apply_transforms(
                child_object,
                location=location,
                rotation=rotation,
                scale=scale,
                recursive=recursive
            )

class DNA_OT_CreateArmature(bpy.types.Operator):
    """Create an armature from the imported DNA file"""
    bl_idname = "dna.create_armature"
    bl_label = "Create Armature from DNA"
    bl_description = "Create an armature with bones from the imported DNA file"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        """Execute the create armature operation"""
        log_info("=== DNA Create Armature Process Started ===")

        # Update status message
        context.scene.dna_tools.status_message = "Creating armature..."
        update_ui()

        # Ensure the DNA modules are in the Python path
        log_info("Ensuring DNA modules path...")
        ensure_dna_modules_path()

        log_info("Checking DNA modules availability...")
        # Check if DNA modules are available
        dna_available = check_dna_modules()
        log_info(f"DNA modules available: {dna_available}")

        if not dna_available:
            log_error("DNA modules not available, cancelling armature creation")
            context.scene.dna_tools.status_message = "Error: DNA modules not available"
            update_ui()
            self.report({'ERROR'}, "DNA modules not available. Check the installation.")
            return {'CANCELLED'}

        log_info("DNA modules check passed, proceeding with armature creation")

        # Check if a DNA file is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            log_error("No DNA file loaded, cancelling armature creation")
            context.scene.dna_tools.status_message = "Error: No DNA file loaded"
            update_ui()
            self.report({'ERROR'}, "No DNA file loaded. Import a DNA file first.")
            return {'CANCELLED'}

        # Check if mesh has been created
        if not dna_tools.is_mesh_created:
            log_error("No mesh created, cancelling armature creation")
            context.scene.dna_tools.status_message = "Error: No mesh created"
            update_ui()
            self.report({'ERROR'}, "No mesh created. Create a mesh first.")
            return {'CANCELLED'}

        # Create the armature
        try:
            log_info("Creating armature from DNA file...")
            context.scene.dna_tools.status_message = "Creating armature from DNA file..."
            update_ui()

            armature_obj = self.create_armature_from_dna(context)

            # Set the flag to indicate armature has been created
            context.scene.dna_tools.is_armature_created = True

            log_info("Armature creation completed successfully")
            context.scene.dna_tools.status_message = "Armature creation completed successfully"
            update_ui()

            return {'FINISHED'}
        except Exception as e:
            log_error(f"Error during armature creation: {str(e)}")
            traceback.print_exc()
            context.scene.dna_tools.status_message = f"Error during armature creation: {str(e)}"
            update_ui()
            self.report({'ERROR'}, f"Error creating armature: {str(e)}")
            return {'CANCELLED'}

    def open_dna_file(self, context):
        """Open and read the DNA file

        Args:
            context: Blender context

        Returns:
            tuple: (reader, model_name, parent_collection)
        """
        # Import the DNA modules
        import dna
        from dna import FileStream, BinaryStreamReader, Status, DataLayer_All

        # Get the DNA file path
        dna_file_path = context.scene.dna_tools.dna_file_path
        log_info(f"Opening DNA file: {dna_file_path}")

        # Open the DNA file
        try:
            stream = FileStream(dna_file_path, FileStream.AccessMode_Read, FileStream.OpenMode_Binary)
            reader = BinaryStreamReader(stream, DataLayer_All)

            # Read the DNA file
            start_time = time.time()
            reader.read()
            end_time = time.time()
            log_info(f"DNA file read in {end_time - start_time:.2f} seconds")

            # Check for errors
            if not dna.Status.isOk():
                status = dna.Status.get()
                log_error(f"Failed to read DNA file: {status.message}")
                raise Exception(f"Failed to read DNA file: {status.message}")

            # Log basic DNA file information
            log_info(f"DNA file loaded successfully")
            log_info(f"Joint count: {reader.getJointCount()}")
            log_info(f"Mesh count: {reader.getMeshCount()}")

            # Get the model name from the file path
            model_name = os.path.splitext(os.path.basename(dna_file_path))[0]
            log_info(f"Model name: {model_name}")

            # Find or create the parent collection
            parent_collection = self.get_or_create_collection(context, model_name)

            return reader, model_name, parent_collection

        except Exception as e:
            log_error(f"Error opening DNA file: {str(e)}")
            raise

    def get_or_create_collection(self, context, collection_name):
        """Get or create a collection

        Args:
            context: Blender context
            collection_name: Name of the collection

        Returns:
            bpy.types.Collection: The collection
        """
        # Find the collection
        log_debug(f"Looking for collection: {collection_name}")
        parent_collection = None
        for collection in bpy.data.collections:
            if collection.name == collection_name:
                parent_collection = collection
                log_debug(f"Found existing collection: {collection_name}")
                break

        # Create the collection if it doesn't exist
        if not parent_collection:
            log_debug(f"Creating new collection: {collection_name}")
            parent_collection = bpy.data.collections.new(collection_name)
            bpy.context.scene.collection.children.link(parent_collection)

        return parent_collection

    def create_armature_object(self, context, name, parent_collection):
        """Create a new armature object

        Args:
            context: Blender context
            name: Name of the armature
            parent_collection: Collection to add the armature to

        Returns:
            bpy.types.Object: The armature object
        """
        log_info(f"Creating armature object: {name}")

        # Remove existing armature with the same name if it exists
        existing_armature = bpy.data.armatures.get(name)
        if existing_armature:
            log_info(f"Removing existing armature: {name}")
            bpy.data.armatures.remove(existing_armature)

        existing_obj = bpy.data.objects.get(name)
        if existing_obj:
            log_info(f"Removing existing object: {name}")
            bpy.data.objects.remove(existing_obj)

        # Create the armature
        armature = bpy.data.armatures.new(name)
        armature_obj = bpy.data.objects.new(name, armature)
        parent_collection.objects.link(armature_obj)

        # Set as active object
        context.view_layer.objects.active = armature_obj

        # Enter edit mode and remove any automatically created bones
        bpy.ops.object.mode_set(mode='EDIT')
        if armature_obj.data.edit_bones:
            for edit_bone in armature_obj.data.edit_bones:
                log_debug(f"Removing automatically created bone: {edit_bone.name}")
                armature_obj.data.edit_bones.remove(edit_bone)
        bpy.ops.object.mode_set(mode='OBJECT')

        log_debug(f"Armature object created: {armature_obj.name}")
        return armature_obj

    def create_armature_from_dna(self, context):
        """Create an armature from the DNA file"""
        log_info("Starting armature creation process")

        # Open the DNA file and get the reader
        reader, model_name, parent_collection = self.open_dna_file(context)

        # Create the armature object - name it "root" as requested
        armature_name = "root"
        armature_obj = self.create_armature_object(context, armature_name, parent_collection)

        # Enter edit mode
        bpy.ops.object.mode_set(mode='EDIT')

        # Double-check that there are no bones in the armature
        log_info("Ensuring no bones exist in the armature...")
        if armature_obj.data.edit_bones:
            for edit_bone in armature_obj.data.edit_bones:
                log_warning(f"Found unexpected bone: {edit_bone.name}, removing it")
                armature_obj.data.edit_bones.remove(edit_bone)

        # Create extra bones first
        log_info("Creating extra bones...")
        context.scene.dna_tools.status_message = "Creating extra bones..."
        update_ui()

        extra_bones = self.create_extra_bones(context, armature_obj, reader)

        # Read joint data
        joint_data = self.read_joint_data(reader)

        # Create bones from DNA data
        log_info("Creating bones from DNA data...")
        context.scene.dna_tools.status_message = "Creating bones from DNA data..."
        update_ui()

        self.create_bones_from_dna(context, armature_obj, joint_data, reader, extra_bones)

        # Exit edit mode
        bpy.ops.object.mode_set(mode='OBJECT')

        # Apply the rotation to the armature to convert from DNA to Blender coordinate system
        log_info("Applying 90-degree X rotation to armature for coordinate system conversion...")

        # Make sure we're in object mode and the armature is selected
        bpy.ops.object.mode_set(mode='OBJECT')
        for obj in bpy.context.selected_objects:
            obj.select_set(False)

        armature_obj.select_set(True)
        bpy.context.view_layer.objects.active = armature_obj

        # Apply the 90-degree X rotation using our utility function
        # This converts from DNA (Z-up, X-forward) to Blender (Z-up, Y-forward) coordinate system
        apply_dna_to_blender_rotation(armature_obj)

        # Apply the rotation to bake it into the armature data
        apply_transforms(armature_obj, rotation=True, recursive=True)

        # Verify the rotation was applied
        if abs(armature_obj.rotation_euler.x) > 0.001:
            log_warning("Rotation was not properly applied to armature. Trying alternative method...")
            # Alternative method to ensure rotation is applied
            bpy.ops.object.select_all(action='DESELECT')
            armature_obj.select_set(True)
            bpy.context.view_layer.objects.active = armature_obj
            bpy.ops.object.transform_apply(location=False, rotation=True, scale=False)

        # Find mesh objects in the parent collection
        mesh_objects = [obj for obj in parent_collection.objects if obj.type == 'MESH']
        log_info(f"Found {len(mesh_objects)} mesh objects in collection")

        # Note: Weight application has been removed from the create armature phase
        log_info("Skipping skinning setup as per requirements...")

        # Create custom bone shapes if enabled
        if context.scene.dna_tools.use_custom_bone_shapes:
            log_info("Creating custom bone shapes...")
            context.scene.dna_tools.status_message = "Creating custom bone shapes..."
            update_ui()

            self.create_custom_bone_shapes(context, armature_obj)

        # Organize bone collections if enabled
        if context.scene.dna_tools.organize_bone_collections:
            log_info("Organizing bone collections...")
            context.scene.dna_tools.status_message = "Organizing bone collections..."
            update_ui()

            self.organize_bone_collections(context, armature_obj, mesh_objects)

        # Note: Fitting bones to mesh has been removed from the create armature phase
        # as it relies on vertex weights which are no longer applied at this stage
        log_info("Skipping fitting bones to mesh as per requirements...")

        # Set the relation line position to HEAD
        armature_obj.data.relation_line_position = 'HEAD'

        log_info("Armature creation completed successfully")
        return armature_obj

    def get_height_scale_factor(self, reader):
        """Calculate the height scale factor based on the first bone's Y location

        Args:
            reader: DNA reader

        Returns:
            float: Height scale factor
        """
        # Get Y locations from the DNA reader
        y_locations = []
        for joint_index in range(reader.getJointCount()):
            y_locations.append(reader.getNeutralJointTranslation(joint_index)[1])

        # Determine the height scale factor based on how much the first bone is moved
        height_scale_factor = 1.0
        if len(y_locations) > 0:
            # Use the same reference value as the example implementation
            height_scale_factor = y_locations[0] / 107.86403  # FIRST_BONE_Y_LOCATION from example

        log_info(f"Height scale factor: {height_scale_factor}")
        return height_scale_factor

    def create_extra_bones(self, context, armature_obj, reader=None):
        """Create extra bones for the standard skeletal hierarchy

        Args:
            context: Blender context
            armature_obj: Armature object
            reader: DNA reader (optional)

        Returns:
            dict: Dictionary of extra bones
        """
        log_info("Creating extra bones")

        # Get armature data
        armature = armature_obj.data

        # Create a dictionary to store extra bones
        extra_bones = {}

        # Calculate height scale factor if reader is provided
        height_scale_factor = 1.0
        if reader:
            height_scale_factor = self.get_height_scale_factor(reader)

        # Linear modifier for scaling (same as used for DNA bones)
        linear_modifier = 0.01  # 1 cm = 0.01 m

        # Create the extra bones, but skip the 'root' bone since we don't need it
        # The armature object itself will be named 'root' during export
        for bone_name, bone_data in EXTRA_BONES:
            # Skip the 'root' bone - we don't need it and it causes issues
            if bone_name == 'root':
                log_info("Skipping 'root' bone creation - not needed for export structure")
                continue

            log_debug(f"Creating extra bone: {bone_name}")

            # Create new edit bone
            edit_bone = armature.edit_bones.new(bone_name)
            extra_bones[bone_name] = edit_bone

            # Get location and rotation from bone data
            location = bone_data['location'].copy()  # Create a copy to avoid modifying the original
            rotation = bone_data['rotation']

            # Scale the Y component of location by the height scale factor
            location.y = location.y * round(height_scale_factor, 4)

            # Set bone length to match the scale of DNA bones
            edit_bone.length = linear_modifier

            # Set parent if it exists
            parent_name = bone_data.get('parent')
            # Skip parent assignment for pelvis since we skipped root
            if bone_name == 'pelvis':
                edit_bone.parent = None  # Pelvis becomes the root bone
                log_info("Pelvis set as root bone (no parent)")
            else:
                # Use the same approach as the example implementation
                # This handles the case where parent_name is None
                edit_bone.parent = armature.edit_bones.get(parent_name or '')

            # Create global matrix from location and rotation for all bones
            global_matrix = Matrix.Translation(location) @ rotation.to_matrix().to_4x4()

            # Set the bone matrix
            edit_bone.matrix = global_matrix

        log_info(f"Created {len(extra_bones)} extra bones")
        return extra_bones

    def read_joint_data(self, reader):
        """Read joint data from the DNA file

        Args:
            reader: DNA reader

        Returns:
            list: List of dictionaries containing joint data
        """
        log_info("Reading joint data from DNA file")

        joint_count = reader.getJointCount()
        log_info(f"Total joint count: {joint_count}")

        # Log all joint names for debugging
        log_info("All joint names in DNA file:")
        for i in range(joint_count):
            log_info(f"  Joint {i}: {reader.getJointName(i)}")

        joint_data = []

        for joint_index in range(joint_count):
            # Get joint name
            joint_name = reader.getJointName(joint_index)

            # Get parent index
            parent_index = reader.getJointParentIndex(joint_index)

            # Get joint position
            x = reader.getNeutralJointTranslationXs()[joint_index]
            y = reader.getNeutralJointTranslationYs()[joint_index]
            z = reader.getNeutralJointTranslationZs()[joint_index]

            # Get joint rotation
            rx = reader.getNeutralJointRotationXs()[joint_index]
            ry = reader.getNeutralJointRotationYs()[joint_index]
            rz = reader.getNeutralJointRotationZs()[joint_index]

            # Store joint data
            joint_data.append({
                'name': joint_name,
                'parent_index': parent_index,
                'position': (x, y, z),
                'rotation': (rx, ry, rz)
            })

            # Log detailed information for the first few joints
            if joint_index < 5 or joint_index == joint_count - 1:
                log_debug(f"Joint {joint_index}: {joint_name}")
                log_debug(f"  Parent: {parent_index}")
                log_debug(f"  Position: ({x}, {y}, {z})")
                log_debug(f"  Rotation: ({rx}, {ry}, {rz})")

                # Log the converted position using our utility function
                blender_pos = dna_to_blender_coords(x, y, z)
                log_debug(f"  Blender Position: ({blender_pos[0]}, {blender_pos[1]}, {blender_pos[2]})")

        # Log parent-child relationships
        log_debug("Joint hierarchy:")
        for joint_index, joint in enumerate(joint_data):
            if joint['parent_index'] == joint_index:
                log_debug(f"Root joint: {joint['name']} (index: {joint_index})")
            else:
                parent_name = joint_data[joint['parent_index']]['name'] if joint['parent_index'] < len(joint_data) else "INVALID"
                log_debug(f"Joint: {joint['name']} (index: {joint_index}), Parent: {parent_name} (index: {joint['parent_index']})")

        log_info(f"Read data for {len(joint_data)} joints")
        return joint_data

    def create_bones_from_dna(self, context, armature_obj, joint_data, reader, extra_bones):
        """Create bones from DNA joint data

        Args:
            context: Blender context
            armature_obj: Armature object
            joint_data: Joint data from read_joint_data
            reader: DNA reader
            extra_bones: Dictionary of extra bones
        """
        log_info("Creating bones from DNA joint data")

        # Get armature data
        armature = armature_obj.data

        # Create a dictionary to store edit bones
        edit_bones = {}

        # Linear modifier for scaling
        linear_modifier = 0.01  # 1 cm = 0.01 m

        # Log the number of joints we're about to create
        log_info(f"About to create {len(joint_data)} bones from joint data")

        # Create bones in hierarchical order to match reference MetaHuman structure
        # This ensures correct sibling bone order by creating bones depth-first
        bone_flags = {}
        created_bones = 0

        # Initialize flags for all bones
        for joint_index, joint in enumerate(joint_data):
            bone_flags[joint['name']] = False

        # Build hierarchy map for efficient traversal
        hierarchy_map = {}
        root_bones = []

        for joint_index, joint in enumerate(joint_data):
            parent_index = joint['parent_index']
            if parent_index == joint_index or parent_index >= len(joint_data):
                # This is a root bone
                root_bones.append(joint_index)
            else:
                # Add to parent's children list
                if parent_index not in hierarchy_map:
                    hierarchy_map[parent_index] = []
                hierarchy_map[parent_index].append(joint_index)

        def create_bone_hierarchical(joint_index, parent_bone=None):
            """Create bones in hierarchical depth-first order"""
            joint = joint_data[joint_index]
            bone_name = joint['name']

            # If bone already created, return it
            if bone_flags[bone_name]:
                return armature.edit_bones.get(bone_name)

            # Check if a bone with this name already exists
            existing_bone = armature.edit_bones.get(bone_name)
            if existing_bone:
                log_warning(f"Bone {bone_name} already exists, using existing")
                edit_bones[joint_index] = existing_bone
                bone_flags[bone_name] = True
                return existing_bone

            # Get joint position and rotation
            x, y, z = joint['position']
            rx, ry, rz = joint['rotation']

            # Convert DNA coordinates to Blender coordinates
            x_scaled, y_scaled, z_scaled = dna_to_blender_coords(x, y, z)
            location = Vector((x_scaled, y_scaled, z_scaled))

            # Create rotation from Euler angles
            euler_rotation = Euler((
                math.radians(rx),
                math.radians(ry),
                math.radians(rz)
            ), 'XYZ')

            # Create new edit bone
            log_debug(f"Creating bone hierarchically: {bone_name}")
            edit_bone = armature.edit_bones.new(bone_name)
            edit_bones[joint_index] = edit_bone
            bone_flags[bone_name] = True

            # Set bone length
            edit_bone.length = linear_modifier

            # Set parent and matrix
            if parent_bone:
                edit_bone.parent = parent_bone

                # Create local matrix
                local_matrix = Matrix.Translation(location) @ euler_rotation.to_matrix().to_4x4()

                # Create global matrix
                global_matrix = parent_bone.matrix @ local_matrix

                # Set bone matrix
                edit_bone.matrix = global_matrix
            else:
                # Root bone - set in object space
                edit_bone.matrix = Matrix.Translation(location) @ euler_rotation.to_matrix().to_4x4()

                # Set parent to extra bones if this is the first DNA bone
                if joint_index == 0:
                    if 'spine_03' in extra_bones:
                        edit_bone.parent = extra_bones['spine_03']
                    elif 'pelvis' in extra_bones:
                        edit_bone.parent = extra_bones['pelvis']

            # Recursively create children in order
            if joint_index in hierarchy_map:
                for child_index in hierarchy_map[joint_index]:
                    create_bone_hierarchical(child_index, edit_bone)

            return edit_bone

        # Create all bones starting from root bones
        for root_index in root_bones:
            create_bone_hierarchical(root_index)
            created_bones += 1

        # Log summary of bone creation
        log_info(f"Bone creation summary:")
        log_info(f"  Total joints in DNA: {len(joint_data)}")
        log_info(f"  Created bones: {created_bones}")
        log_info(f"  Total bones in edit_bones dictionary: {len(edit_bones)}")

        # Log all bones in the armature
        log_info(f"Bones in armature after creation:")
        for i, bone in enumerate(armature.edit_bones):
            log_info(f"  {i}: {bone.name}")

        log_info(f"Created {len(edit_bones)} bones from DNA data")

    def setup_skinning(self, context, armature_obj, mesh_objects, reader):
        """Setup skinning for mesh objects

        Args:
            context: Blender context
            armature_obj: Armature object
            mesh_objects: List of mesh objects from the collection
            reader: DNA reader
        """
        log_info("Setting up skinning")

        # Exit edit mode if we're in it
        if armature_obj.mode == 'EDIT':
            bpy.ops.object.mode_set(mode='OBJECT')

        # Get all mesh objects in the scene that might have been created by create_model.py
        all_mesh_objects = [obj for obj in bpy.data.objects if obj.type == 'MESH']
        log_info(f"Found {len(all_mesh_objects)} total mesh objects in the scene")

        # Get joint names from the DNA file for matching
        joint_names = [reader.getJointName(i) for i in range(reader.getJointCount())]

        # Track which meshes we've processed
        processed_meshes = set()

        # First process the mesh objects that were directly passed to this function
        for mesh_obj in mesh_objects:
            if mesh_obj.name in processed_meshes:
                continue

            log_info(f"Setting up skinning for mesh from collection: {mesh_obj.name}")

            # Parent to armature
            mesh_obj.parent = armature_obj
            log_debug(f"Parented {mesh_obj.name} to {armature_obj.name}")

            # Add an armature modifier
            modifier = mesh_obj.modifiers.new(name="Armature", type='ARMATURE')
            modifier.object = armature_obj
            log_debug(f"Added armature modifier to {mesh_obj.name}")

            # Create vertex groups and assign weights
            self.create_vertex_groups_and_weights(mesh_obj, reader)

            # Mark as processed
            processed_meshes.add(mesh_obj.name)

        # Now look for other meshes in the scene that might have been created by create_model.py
        for mesh_obj in all_mesh_objects:
            # Skip already processed meshes
            if mesh_obj.name in processed_meshes:
                continue

            # Check if this mesh has vertex groups that match joint names from the DNA file
            # This is a good indicator that it was created by create_model.py
            has_dna_vertex_groups = False
            if mesh_obj.vertex_groups:
                # Check if any vertex group names match joint names from the DNA file
                for vg in mesh_obj.vertex_groups:
                    if vg.name in joint_names:
                        has_dna_vertex_groups = True
                        break

            if has_dna_vertex_groups:
                log_info(f"Found mesh with DNA vertex groups: {mesh_obj.name}")

                # Parent to armature
                mesh_obj.parent = armature_obj
                log_debug(f"Parented {mesh_obj.name} to {armature_obj.name}")

                # Add an armature modifier
                # Check if it already has an armature modifier
                has_armature_modifier = False
                for modifier in mesh_obj.modifiers:
                    if modifier.type == 'ARMATURE':
                        has_armature_modifier = True
                        # Update the modifier to use our armature
                        modifier.object = armature_obj
                        log_debug(f"Updated existing armature modifier on {mesh_obj.name}")
                        break

                # If no armature modifier exists, create one
                if not has_armature_modifier:
                    modifier = mesh_obj.modifiers.new(name="Armature", type='ARMATURE')
                    modifier.object = armature_obj
                    log_debug(f"Added armature modifier to {mesh_obj.name}")

                # Mark as processed
                processed_meshes.add(mesh_obj.name)

        log_info(f"Skinning setup completed for {len(processed_meshes)} meshes")

    def create_vertex_groups_and_weights(self, mesh_obj, reader):
        """Create vertex groups and assign weights

        Args:
            mesh_obj: Mesh object
            reader: DNA reader
        """
        log_info(f"Creating vertex groups and weights for {mesh_obj.name}")

        # Find the mesh index in the DNA file
        mesh_index = -1
        for i in range(reader.getMeshCount()):
            if reader.getMeshName(i) == mesh_obj.name:
                mesh_index = i
                log_debug(f"Found mesh index: {mesh_index}")
                break

        if mesh_index == -1:
            log_warning(f"Could not find mesh {mesh_obj.name} in DNA file")
            return

        # Create vertex groups for each joint
        joint_count = reader.getJointCount()
        log_debug(f"Creating {joint_count} vertex groups")

        # Create vertex groups for all joints, including extra bones
        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)

            # Create vertex group if it doesn't exist
            if joint_name not in mesh_obj.vertex_groups:
                mesh_obj.vertex_groups.new(name=joint_name)
                log_debug(f"Created vertex group: {joint_name}")

        # Create vertex groups for extra bones
        for bone_name, _ in EXTRA_BONES:
            if bone_name not in mesh_obj.vertex_groups:
                mesh_obj.vertex_groups.new(name=bone_name)
                log_debug(f"Created vertex group for extra bone: {bone_name}")

        # Get skinning information
        try:
            skin_count = reader.getSkinningCount(mesh_index)
            log_info(f"Assigning {skin_count} skin weights")

            # Track statistics for debugging
            assigned_weights = 0
            max_weight = 0
            min_weight = 1

            # Get all skinning data at once for better performance
            joint_indices = reader.getSkinningJointIndices(mesh_index)
            vertex_indices = reader.getSkinningVertexIndices(mesh_index)
            weights = reader.getSkinningWeights(mesh_index)

            # Create a dictionary to batch vertex group assignments
            weight_data = {}

            # Process all skinning data
            for skin_index in range(skin_count):
                # Get joint index and name
                joint_index = joint_indices[skin_index]
                joint_name = reader.getJointName(joint_index)

                # Get vertex index and weight
                vertex_index = vertex_indices[skin_index]
                weight = weights[skin_index]

                # Update statistics
                max_weight = max(max_weight, weight)
                min_weight = min(min_weight, weight)

                # Add to weight data dictionary
                if joint_name not in weight_data:
                    weight_data[joint_name] = []
                weight_data[joint_name].append((vertex_index, weight))

                # Log some sample weights for debugging
                if skin_index < 5 or skin_index == skin_count - 1:
                    log_debug(f"Weight: Joint {joint_name} (index: {joint_index}), Vertex: {vertex_index}, Weight: {weight}")

            # Assign weights in batches for better performance
            for joint_name, vertex_weights in weight_data.items():
                vertex_group = mesh_obj.vertex_groups.get(joint_name)
                if not vertex_group:
                    log_warning(f"Vertex group {joint_name} not found")
                    continue

                # Group vertices by weight for batch assignment
                weight_groups = {}
                for vertex_index, weight in vertex_weights:
                    if weight not in weight_groups:
                        weight_groups[weight] = []
                    weight_groups[weight].append(vertex_index)

                # Assign weights in batches
                for weight, vertices in weight_groups.items():
                    vertex_group.add(vertices, weight, 'REPLACE')
                    assigned_weights += len(vertices)

            log_info(f"Assigned {assigned_weights} weights (min: {min_weight}, max: {max_weight})")

        except Exception as e:
            log_error(f"Error assigning weights: {str(e)}")
            traceback.print_exc()

    def create_custom_bone_shapes(self, context, armature_obj):
        """Create custom bone shapes for better visualization

        Args:
            context: Blender context
            armature_obj: Armature object
        """
        log_info("Creating custom bone shapes")

        # Create a custom bone shape
        bone_shape = self.create_bone_shape(context, CUSTOM_BONE_SHAPE_NAME)

        # Check if bone shape creation was successful
        if not bone_shape:
            log_error("Failed to create bone shape, skipping custom bone shapes")
            return

        # Enter pose mode
        bpy.ops.object.mode_set(mode='POSE')

        # Apply custom shape to all bones
        bone_count = len(armature_obj.pose.bones)
        log_debug(f"Applying custom shape to {bone_count} bones")

        # Identify face bones (bones with 'face' in their name or starting with 'FACIAL_')
        face_bones = []
        for pose_bone in armature_obj.pose.bones:
            # Set rotation mode to XYZ for all bones
            pose_bone.rotation_mode = 'XYZ'

            # Apply custom shape
            pose_bone.custom_shape = bone_shape

            # Check if this is a face bone
            if 'face' in pose_bone.name.lower() or pose_bone.name.startswith('FACIAL_'):
                face_bones.append(pose_bone.name)

                face_bone_scale = CUSTOM_BONE_SHAPE_SCALE.x/5
                pose_bone.custom_shape_scale_xyz = Vector((face_bone_scale,
                                                          face_bone_scale,
                                                          face_bone_scale))
            else:
                # Use normal scale for other bones
                pose_bone.custom_shape_scale_xyz = CUSTOM_BONE_SHAPE_SCALE

        # We'll create bone collections in the organize_bone_collections method instead
        log_debug(f"Identified {len(face_bones)} face bones")

        log_info("Custom bone shapes created")

    def create_bone_shape(self, context, name=CUSTOM_BONE_SHAPE_NAME):
        """Create a custom bone shape

        Args:
            context: Blender context
            name: Name of the bone shape

        Returns:
            bpy.types.Object: The bone shape object
        """
        log_info(f"Creating bone shape: {name}")

        # Check if the shape already exists
        if name in bpy.data.objects:
            log_debug(f"Bone shape {name} already exists, reusing")
            return bpy.data.objects[name]

        # Create three circles in different orientations
        rotations = [
            (90, 0, 0),  # XY plane
            (0, 90, 0),  # YZ plane
            (0, 0, 90)   # XZ plane
        ]

        # Store the current active object and selection
        original_active = context.active_object
        original_selected_objects = [obj for obj in context.selected_objects]

        # Deselect all objects
        bpy.ops.object.select_all(action='DESELECT')

        log_debug("Creating circles for bone shape")
        circles = []
        for i, rotation in enumerate(rotations):
            bpy.ops.mesh.primitive_circle_add(
                vertices=16,
                radius=1,
                enter_editmode=False,
                align='WORLD',
                location=(0, 0, 0),
                scale=(1, 1, 1),
                rotation=(
                    math.radians(rotation[0]),
                    math.radians(rotation[1]),
                    math.radians(rotation[2])
                )
            )
            # Get the newly created circle
            circle = context.active_object
            if circle:
                circles.append(circle)
                log_debug(f"Created circle {i+1} with rotation {rotation}")
            else:
                log_error(f"Failed to create circle {i+1}")

        # Check if we have any circles
        if not circles:
            log_error("No circles were created, cannot create bone shape")
            # Restore original selection
            for obj in original_selected_objects:
                obj.select_set(True)
            if original_active:
                context.view_layer.objects.active = original_active
            return None

        # Select all circles
        for circle in circles:
            circle.select_set(True)

        # Set the active object to the first circle
        context.view_layer.objects.active = circles[0]

        # Join the circles
        bpy.ops.object.join()
        log_debug("Joined circles into one object")

        # Get the joined object
        bone_shape = context.active_object
        if not bone_shape:
            log_error("Failed to join circles, active object is None")
            # Restore original selection
            for obj in original_selected_objects:
                obj.select_set(True)
            if original_active:
                context.view_layer.objects.active = original_active
            return None

        # Rename the resulting object
        bone_shape.name = name

        # Hide the shape from the viewport but keep it in the scene
        bone_shape.hide_viewport = True
        bone_shape.hide_render = True

        # Set fake user to prevent it from being deleted
        bone_shape.use_fake_user = True

        # Move to a collection that won't be rendered
        # First check if we have a "Hidden" collection, create if not
        hidden_collection = bpy.data.collections.get("Hidden")
        if not hidden_collection:
            hidden_collection = bpy.data.collections.new("Hidden")
            bpy.context.scene.collection.children.link(hidden_collection)
            # Set the collection to be hidden
            layer_collection = bpy.context.view_layer.layer_collection.children.get("Hidden")
            if layer_collection:
                layer_collection.hide_viewport = True

        # Move the bone shape to the hidden collection
        # First remove from current collections
        for collection in bone_shape.users_collection:
            collection.objects.unlink(bone_shape)
        # Then add to hidden collection
        hidden_collection.objects.link(bone_shape)

        log_info(f"Bone shape {name} created")

        # Restore original selection
        bpy.ops.object.select_all(action='DESELECT')
        for obj in original_selected_objects:
            obj.select_set(True)
        if original_active:
            context.view_layer.objects.active = original_active

        return bone_shape

    def organize_bone_collections(self, context, armature_obj, mesh_objects):
        """Organize bones into further simplified collections as requested:
        1. Face Bones - All facial bones
        2. Body Bones - All non-face bones

        Args:
            context: Blender context
            armature_obj: Armature object
            mesh_objects: List of mesh objects
        """
        log_info("Organizing bone collections")

        # Enter pose mode
        bpy.ops.object.mode_set(mode='POSE')

        # Lists for our two bone collections
        face_bones = []
        body_bones = []

        # Process all bones in the armature
        for pose_bone in armature_obj.pose.bones:
            # Clean up bone name (remove any prefixes like 'DHIhead:')
            original_name = pose_bone.name
            if ':' in original_name:
                new_name = original_name.split(':', 1)[1]
                # Rename the bone
                pose_bone.name = new_name
                log_debug(f"Renamed bone from {original_name} to {new_name}")

            bone_name = pose_bone.name

            # Check if this is a face bone
            is_face_bone = 'face' in bone_name.lower() or bone_name.startswith('FACIAL_')

            # Categorize the bone
            if is_face_bone:
                face_bones.append(bone_name)
            else:
                body_bones.append(bone_name)

        # Log bone collection statistics
        log_debug(f"Face bones: {len(face_bones)}")
        log_debug(f"Body bones: {len(body_bones)}")

        # Create bone collections with different colors
        self.create_bone_collection(armature_obj, face_bones, BoneCollection.FACE_BONES, "THEME10")  # Bright Red
        self.create_bone_collection(armature_obj, body_bones, BoneCollection.BODY_BONES, "THEME06")  # Blue

        log_info("Bone collections organized")

    def create_bone_collection(self, armature_obj, bone_names, collection_name, theme=None):
        """Create a bone collection and assign bones to it

        Args:
            armature_obj: Armature object
            bone_names: List of bone names
            collection_name: Name of the collection
            theme: Color theme for the bones
        """
        log_debug(f"Creating bone collection: {collection_name} with {len(bone_names)} bones")

        # Get or create a new bone collection
        collection = armature_obj.data.collections.get(collection_name)
        if not collection:
            collection = armature_obj.data.collections.new(name=collection_name)
            log_debug(f"Created new bone collection: {collection_name}")

        # Assign bones to the collection
        for bone_name in bone_names:
            bone = armature_obj.data.bones.get(bone_name)
            if bone and theme:
                bone.color.palette = theme

            pose_bone = armature_obj.pose.bones.get(bone_name)
            if pose_bone:
                collection.assign(pose_bone)
                if theme:
                    pose_bone.color.palette = theme

    def get_bmesh(self, mesh_obj, rotation=0):
        """Get a BMesh object from a mesh object

        Args:
            mesh_obj: Mesh object
            rotation: Rotation to apply (in degrees)

        Returns:
            bmesh.BMesh: BMesh object
        """
        import bmesh

        # Create a new bmesh
        bmesh_obj = bmesh.new()

        # Copy mesh data to bmesh
        bmesh_obj.from_mesh(mesh_obj.data)

        # Apply rotation if needed
        if rotation != 0:
            # Create rotation matrix
            rotation_matrix = Matrix.Rotation(math.radians(rotation), 4, 'X')

            # Apply rotation to vertices
            bmesh.ops.transform(bmesh_obj, matrix=rotation_matrix, verts=bmesh_obj.verts)

        # Ensure lookup tables
        bmesh_obj.verts.ensure_lookup_table()
        bmesh_obj.edges.ensure_lookup_table()
        bmesh_obj.faces.ensure_lookup_table()

        return bmesh_obj

    def get_mesh_vertex_positions(self, bmesh_obj):
        """Get vertex positions from a BMesh object

        Args:
            bmesh_obj: BMesh object

        Returns:
            tuple: (vertex_indices, vertex_positions)
        """
        # Get vertex indices and positions
        vertex_indices = []
        vertex_positions = []

        for vertex in bmesh_obj.verts:
            vertex_indices.append(vertex.index)
            vertex_positions.append(vertex.co.copy())

        return vertex_indices, vertex_positions

    def get_bone_transforms(self, armature_obj):
        """Get bone transforms from an armature object

        Args:
            armature_obj: Armature object

        Returns:
            dict: Dictionary of bone transforms
        """
        # Get bone transforms
        bone_transforms = {}

        # Enter edit mode
        bpy.ops.object.mode_set(mode='EDIT')

        # Get armature data
        armature = armature_obj.data

        # Get bone data
        for bone in armature.edit_bones:
            bone_transforms[bone.name] = {
                'head': bone.head.copy(),
                'tail': bone.tail.copy(),
                'matrix': bone.matrix.copy(),
                'parent': bone.parent.name if bone.parent else None
            }

        # Exit edit mode
        bpy.ops.object.mode_set(mode='OBJECT')

        return bone_transforms

    def fit_bones_to_mesh(self, context, armature_obj, mesh_obj, reader):
        """Fit bones to the mesh for better alignment

        Args:
            context: Blender context
            armature_obj: Armature object
            mesh_obj: Mesh object
            reader: DNA reader
        """
        log_info(f"Fitting bones to mesh: {mesh_obj.name}")

        # Import the rotation utilities
        from ..utils.rotation_utils import calculate_fitted_bone_positions

        # Use our centralized function to calculate fitted bone positions
        result = calculate_fitted_bone_positions(
            mesh_obj=mesh_obj,
            armature_obj=armature_obj,
            dna_reader=reader,
            parent_depth=1,
            factor=1.0,
            only_bone_names=None
        )

        # Enter edit mode
        bpy.ops.object.mode_set(mode='EDIT')

        # Get armature data
        armature = armature_obj.data

        # Apply the calculated bone positions
        adjusted_bones = 0
        for bone_name, (head, tail) in result['bone_positions'].items():
            edit_bone = armature.edit_bones.get(bone_name)
            if edit_bone:
                # Log original and new positions for debugging
                if adjusted_bones < 5:
                    log_debug(f"Adjusting bone: {bone_name}")
                    log_debug(f"  Original position: {edit_bone.head}")
                    log_debug(f"  New position: {head}")

                # Update bone positions
                edit_bone.head = head
                edit_bone.tail = tail
                adjusted_bones += 1

        # Exit edit mode
        bpy.ops.object.mode_set(mode='OBJECT')

        log_info(f"Adjusted {adjusted_bones} bones to fit the mesh")

# Classes to register
classes = [
    DNA_OT_CreateArmature,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)
    print("DNA Armature Operator registered")

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    print("DNA Armature Operator unregistered")
