"""
Operators for the DNA Editor panel.
"""

import os
import bpy
import math
import traceback
from bpy.types import Operator
from bpy.props import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roperty, EnumProperty
from mathutils import Vector, Matrix, Euler, Quaternion

# Import logging functions
from ..utils.logging_utils import log_info, log_error, log_warning, log_debug
from ..utils.dna_utils import ensure_dna_modules_path, check_dna_modules
from ..utils.rotation_utils import blender_to_dna_coords, BLENDER_TO_DNA_MATRIX, BLENDER_TO_DNA_SCALE

class DNA_OT_ExportEditedDNA(Operator):
    """Export the edited DNA file"""
    bl_idname = "dna.export_edited_dna"
    bl_label = "Export Edited DNA"
    bl_description = "Export the DNA file with all edits applied"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        """Only enable if DNA is loaded and model is created"""
        dna_tools = context.scene.dna_tools
        return dna_tools.is_dna_loaded and dna_tools.is_mesh_created

    def execute(self, context):
        dna_tools = context.scene.dna_tools
        dna_editor = context.scene.dna_editor

        # Check if output folder is set
        if not dna_editor.output_folder:
            self.report({'ERROR'}, "Output folder not set")
            return {'CANCELLED'}

        # Create output folder if it doesn't exist
        os.makedirs(bpy.path.abspath(dna_editor.output_folder), exist_ok=True)

        # Construct output path
        output_path = os.path.join(
            bpy.path.abspath(dna_editor.output_folder),
            dna_editor.output_filename
        )

        # Log export information
        log_info(f"Exporting edited DNA to: {output_path}")

        # Ensure DNA modules are available
        ensure_dna_modules_path()
        if not check_dna_modules():
            log_error("DNA modules not available, cannot export DNA")
            self.report({'ERROR'}, "DNA modules not available. Check the installation.")
            return {'CANCELLED'}

        try:
            # Import DNA modules
            import dna
            from dna import FileStream, BinaryStreamReader, BinaryStreamWriter, Status, DataLayer_All

            # Open the source DNA file
            source_dna_path = dna_tools.dna_file_path
            log_info(f"Opening source DNA file: {source_dna_path}")

            # Create a file stream for reading the source DNA
            source_stream = FileStream(source_dna_path, FileStream.AccessMode_Read, FileStream.OpenMode_Binary)
            source_reader = BinaryStreamReader(source_stream, DataLayer_All)

            # Read the source DNA
            source_reader.read()

            # Check for errors
            if not Status.isOk():
                status = Status.get()
                error_msg = f"Error reading source DNA: {status.message}"
                log_error(error_msg)
                self.report({'ERROR'}, error_msg)
                return {'CANCELLED'}

            # Create a file stream for writing the target DNA
            target_stream = FileStream(output_path, FileStream.AccessMode_Write, FileStream.OpenMode_Binary)

            # Always use binary format for export
            target_writer = BinaryStreamWriter(target_stream)

            # Copy all data from the source to the target
            # The UnknownLayerPolicy enum might have different naming in different DNA API versions
            try:
                # Try the underscore version first (newer API)
                target_writer.setFrom(source_reader, DataLayer_All, dna.UnknownLayerPolicy_Preserve)
            except AttributeError:
                try:
                    # Try the dot version (older API)
                    target_writer.setFrom(source_reader, DataLayer_All, dna.UnknownLayerPolicy.Preserve)
                except AttributeError:
                    # If both fail, try without the policy parameter
                    target_writer.setFrom(source_reader, DataLayer_All)

            # Now update the data in the target with the edited values
            self.update_dna_data(context, source_reader, target_writer)

            # Write the target DNA
            target_writer.write()

            # Check for errors
            if not Status.isOk():
                status = Status.get()
                error_msg = f"Error writing target DNA: {status.message}"
                log_error(error_msg)
                self.report({'ERROR'}, error_msg)
                return {'CANCELLED'}

            log_info(f"DNA exported successfully to: {output_path}")
            self.report({'INFO'}, f"DNA exported successfully to: {output_path}")
            return {'FINISHED'}

        except Exception as e:
            log_error(f"Error exporting DNA: {str(e)}")
            traceback.print_exc()
            self.report({'ERROR'}, f"Error exporting DNA: {str(e)}")
            return {'CANCELLED'}

    def update_dna_data(self, context, reader, writer):
        """Update the DNA data with the edited values

        Args:
            context: Blender context
            reader: DNA reader
            writer: DNA writer
        """
        log_info("Updating DNA data with edited values")

        # Update meshes if they exist
        if context.scene.dna_tools.is_mesh_created:
            self.update_mesh_data(context, reader, writer)

            # Update blendshapes if they exist
            self.update_blendshape_data(context, reader, writer)

        # Update bones if they exist
        if context.scene.dna_tools.is_armature_created:
            self.update_bone_data(context, reader, writer)

    def update_mesh_data(self, context, reader, writer):
        """Update mesh data in the DNA

        Args:
            context: Blender context
            reader: DNA reader
            writer: DNA writer
        """
        log_info("Updating mesh data")

        # Get the model name from the DNA file path
        dna_file_path = context.scene.dna_tools.dna_file_path
        model_name = os.path.splitext(os.path.basename(dna_file_path))[0]

        # Find the collection for this model
        model_collection = None
        for collection in bpy.data.collections:
            if collection.name == model_name:
                model_collection = collection
                break

        if not model_collection:
            log_warning(f"Could not find collection for model: {model_name}")
            return

        # Get all LOD collections
        lod_collections = {}
        for collection in bpy.data.collections:
            if collection.name.startswith(f"{model_name}_LOD") or collection.name.endswith(f"_LOD0"):
                try:
                    # Extract the LOD level from the collection name
                    if collection.name.startswith(f"{model_name}_LOD"):
                        lod_level = int(collection.name.split("_LOD")[1])
                    else:  # Handle case like "MH_Friend_LOD0"
                        lod_level = int(collection.name.split("_LOD")[1])

                    lod_collections[lod_level] = collection
                    log_info(f"Found LOD collection: {collection.name} (LOD {lod_level})")
                except (ValueError, IndexError):
                    log_warning(f"Could not extract LOD level from collection name: {collection.name}")

        # Get all mesh objects from all LOD collections
        mesh_objects = []
        for lod_level, collection in lod_collections.items():
            for obj in collection.objects:
                if obj.type == 'MESH':
                    mesh_objects.append((obj, lod_level))

        log_info(f"Found {len(mesh_objects)} mesh objects in all LOD collections")

        # Process each mesh
        for mesh_obj, mesh_lod in mesh_objects:
            # Find the corresponding mesh in the DNA
            mesh_name = mesh_obj.name
            mesh_index = -1

            for i in range(reader.getMeshCount()):
                if reader.getMeshName(i) == mesh_name:
                    mesh_index = i
                    break

            if mesh_index == -1:
                log_warning(f"Could not find mesh in DNA: {mesh_name}")
                continue

            log_info(f"Processing mesh: {mesh_name} (index: {mesh_index}, LOD: {mesh_lod})")

            # Get the mesh data
            mesh = mesh_obj.data

            # We need to capture the current state of the mesh, including any edits made in Blender
            # and then transform it back to DNA space by applying a -90 degree X-axis rotation

            # Get the world matrix of the mesh object to capture all transformations
            # This includes any edits made to the mesh in Blender
            world_matrix = mesh_obj.matrix_world.copy()

            # Get the vertex positions and apply all transformations
            vertex_positions = []

            for v in mesh.vertices:
                # Get the vertex position in local space
                local_pos = Vector((v.co.x, v.co.y, v.co.z))

                # Transform to world space to capture all edits
                world_pos = world_matrix @ local_pos

                # Apply the rotation matrix to convert back to DNA space
                dna_pos = BLENDER_TO_DNA_MATRIX @ world_pos

                # Keep in Blender units to match FBX export scale (no scale conversion)
                x, y, z = dna_pos.x, dna_pos.y, dna_pos.z

                vertex_positions.append([x, y, z])

            log_info(f"Captured and transformed {len(vertex_positions)} vertices for mesh: {mesh_name} (LOD {mesh_lod})")

            # Update the vertex positions in the DNA
            writer.setVertexPositions(mesh_index, vertex_positions)

            log_info(f"Updated vertex positions for mesh: {mesh_name} (LOD {mesh_lod})")

    def update_blendshape_data(self, context, reader, writer):
        """Update blendshape data in the DNA

        Args:
            context: Blender context
            reader: DNA reader
            writer: DNA writer
        """
        log_info("Updating blendshape data")

        # Get the model name from the DNA file path
        dna_file_path = context.scene.dna_tools.dna_file_path
        model_name = os.path.splitext(os.path.basename(dna_file_path))[0]

        # Find all LOD collections
        lod_collections = {}
        for collection in bpy.data.collections:
            if collection.name.startswith(f"{model_name}_LOD") or collection.name.endswith(f"_LOD0"):
                try:
                    # Extract the LOD level from the collection name
                    if collection.name.startswith(f"{model_name}_LOD"):
                        lod_level = int(collection.name.split("_LOD")[1])
                    else:  # Handle case like "MH_Friend_LOD0"
                        lod_level = int(collection.name.split("_LOD")[1])

                    lod_collections[lod_level] = collection
                    log_info(f"Found LOD collection for blendshapes: {collection.name} (LOD {lod_level})")
                except (ValueError, IndexError):
                    log_warning(f"Could not extract LOD level from collection name: {collection.name}")

        # Get all mesh objects from all LOD collections
        mesh_objects = []
        for lod_level, collection in lod_collections.items():
            for obj in collection.objects:
                if obj.type == 'MESH':
                    mesh_objects.append((obj, lod_level))

        log_info(f"Found {len(mesh_objects)} mesh objects for blendshape processing")

        # Process each mesh
        for mesh_obj, mesh_lod in mesh_objects:
            # Skip meshes without shape keys
            if not mesh_obj.data.shape_keys:
                log_info(f"Mesh {mesh_obj.name} has no shape keys, skipping blendshape export")
                continue

            # Find the corresponding mesh in the DNA
            mesh_name = mesh_obj.name
            mesh_index = -1

            for i in range(reader.getMeshCount()):
                if reader.getMeshName(i) == mesh_name:
                    mesh_index = i
                    break

            if mesh_index == -1:
                log_warning(f"Could not find mesh in DNA for blendshapes: {mesh_name}")
                continue

            # Get the number of blendshape targets for this mesh
            blendshape_target_count = reader.getBlendShapeTargetCount(mesh_index)
            if blendshape_target_count == 0:
                log_info(f"No blendshape targets found in DNA for mesh: {mesh_name}")
                continue

            log_info(f"Processing blendshapes for mesh: {mesh_name} (index: {mesh_index}, LOD: {mesh_lod})")
            log_info(f"Found {blendshape_target_count} blendshape targets in DNA")

            # Get the shape keys from the mesh
            shape_keys = mesh_obj.data.shape_keys.key_blocks
            log_info(f"Found {len(shape_keys)} shape keys in Blender (including Basis)")

            # Import the mesh utilities
            from ..utils.mesh_utils import get_shape_key_container

            # Get the shape key container for this mesh
            shape_key_container = get_shape_key_container(mesh_obj)

            # Check if the shape key container name matches the mesh name
            # If not, update it to match the example plugin's approach
            if shape_key_container and shape_key_container.name != mesh_obj.name:
                log_info(f"Updating shape key container name from {shape_key_container.name} to {mesh_obj.name}")
                shape_key_container.name = mesh_obj.name

            # Skip the Basis shape key (index 0)
            # Create a dictionary of shape keys, handling both prefixed and unprefixed names
            shape_keys_dict = {}
            for key in shape_keys:
                if key.name != "Basis":
                    # Store the shape key with its original name
                    shape_keys_dict[key.name] = key

                    # If the shape key has a prefix (meshname__shapekey), also store it with the unprefixed name
                    if "__" in key.name:
                        _, unprefixed_name = key.name.split("__", 1)
                        shape_keys_dict[unprefixed_name] = key

            log_info(f"Found {len(shape_keys_dict)} non-Basis shape keys in Blender")

            # Process each blendshape target
            for target_index in range(blendshape_target_count):
                try:
                    # Get the channel index and name
                    channel_index = reader.getBlendShapeChannelIndex(mesh_index, target_index)
                    channel_name = reader.getBlendShapeChannelName(channel_index)

                    # Check if this blendshape exists in Blender (either with or without prefix)
                    if channel_name not in shape_keys_dict:
                        # Also check for the prefixed version
                        prefixed_name = f"{mesh_name}__{channel_name}"
                        if prefixed_name not in shape_keys_dict:
                            log_warning(f"Blendshape {channel_name} not found in Blender (also checked {prefixed_name}), skipping")
                            continue

                    log_info(f"Processing blendshape: {channel_name} (target index: {target_index}, channel index: {channel_index})")

                    # Get the shape key from Blender
                    shape_key = shape_keys_dict[channel_name]

                    # Get the vertex indices affected by this blendshape in the DNA
                    vertex_indices = list(reader.getBlendShapeTargetVertexIndices(mesh_index, target_index))

                    # Create lists to store the updated delta values
                    delta_xs = []
                    delta_ys = []
                    delta_zs = []

                    # Find the basis shape key - should be named "Basis" according to the example plugin
                    basis_key = None
                    for key in shape_keys:
                        if key.name == "Basis":
                            basis_key = key
                            break

                    # If no basis key found, use the first shape key as fallback
                    if not basis_key:
                        basis_key = shape_keys[0]
                        log_warning(f"Could not find Basis shape key for mesh {mesh_obj.name}, using first shape key as fallback")

                    # Get the world matrix of the mesh object to capture all transformations
                    world_matrix = mesh_obj.matrix_world.copy()

                    # Process each vertex affected by this blendshape
                    for i, vertex_index in enumerate(vertex_indices):
                        if vertex_index < len(shape_key.data):
                            # Get the basis position
                            basis_pos = Vector((basis_key.data[vertex_index].co.x,
                                               basis_key.data[vertex_index].co.y,
                                               basis_key.data[vertex_index].co.z))

                            # Get the shape key position
                            shape_pos = Vector((shape_key.data[vertex_index].co.x,
                                               shape_key.data[vertex_index].co.y,
                                               shape_key.data[vertex_index].co.z))

                            # Calculate the delta in local space
                            local_delta = shape_pos - basis_pos

                            # Transform the delta to world space
                            # We only need to apply the rotation part of the world matrix, not the translation
                            world_delta = world_matrix.to_3x3() @ local_delta

                            # Apply the rotation matrix to convert back to DNA space using our utility constant
                            dna_delta = BLENDER_TO_DNA_MATRIX.to_3x3() @ world_delta

                            # Keep in Blender units to match FBX export scale (no scale conversion)
                            dx, dy, dz = dna_delta.x, dna_delta.y, dna_delta.z

                            # Add to the delta lists
                            delta_xs.append(dx)
                            delta_ys.append(dy)
                            delta_zs.append(dz)

                            # Log some sample deltas for debugging
                            if i < 5 or i == len(vertex_indices) - 1:
                                log_info(f"Updated delta for vertex {vertex_index}: ({dx:.4f}, {dy:.4f}, {dz:.4f})")

                    # Update the blendshape target deltas in the DNA
                    writer.setBlendShapeTargetDeltas(mesh_index, target_index, delta_xs, delta_ys, delta_zs)

                    log_info(f"Updated blendshape {channel_name} with {len(delta_xs)} vertex deltas")

                except Exception as e:
                    log_error(f"Error updating blendshape target {target_index}: {str(e)}")
                    traceback.print_exc()

        log_info("Blendshape data update completed")

    def update_bone_data(self, context, reader, writer):
        """Update bone data in the DNA

        Args:
            context: Blender context
            reader: DNA reader
            writer: DNA writer
        """
        log_info("Updating bone data")

        # Get the armature object
        armature_obj = None
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE' and obj.name == "root":
                armature_obj = obj
                break

        if not armature_obj:
            log_warning("Could not find armature object")
            return

        # Get the joint count
        joint_count = reader.getJointCount()
        log_info(f"Joint count in DNA: {joint_count}")

        # Create lists to store the updated joint data
        joint_translations = []
        joint_rotations_x = []
        joint_rotations_y = []
        joint_rotations_z = []

        # Get the original joint data
        original_translations = []
        for i in range(joint_count):
            x = reader.getNeutralJointTranslationXs()[i]
            y = reader.getNeutralJointTranslationYs()[i]
            z = reader.getNeutralJointTranslationZs()[i]
            original_translations.append([x, y, z])

        original_rotations_x = reader.getNeutralJointRotationXs()
        original_rotations_y = reader.getNeutralJointRotationYs()
        original_rotations_z = reader.getNeutralJointRotationZs()

        # Enter edit mode to get the bone rest transforms (following example implementation)
        bpy.ops.object.mode_set(mode='OBJECT')
        bpy.ops.object.select_all(action='DESELECT')
        armature_obj.select_set(True)
        bpy.context.view_layer.objects.active = armature_obj
        armature_obj.hide_set(False)
        bpy.ops.object.mode_set(mode='EDIT')

        # Change the rotation of the bones since DNA expects Y-up (following example implementation)
        rotation_x = Matrix.Rotation(math.radians(-90), 4, 'X')
        global_matrix = rotation_x.to_4x4()

        # Get edit bones (rest pose) instead of pose bones
        edit_bones = armature_obj.data.edit_bones

        # Create a mapping from joint names to edit bones
        edit_bone_lookup = {}
        for edit_bone in edit_bones:
            edit_bone_lookup[edit_bone.name] = edit_bone

        # Process each joint
        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)

            # Find the corresponding edit bone in the armature
            edit_bone = edit_bone_lookup.get(joint_name)
            if not edit_bone:
                # If the bone doesn't exist, use the original data
                log_warning(f"Could not find edit bone for joint: {joint_name}")
                joint_translations.append(original_translations[joint_index])
                joint_rotations_x.append(original_rotations_x[joint_index])
                joint_rotations_y.append(original_rotations_y[joint_index])
                joint_rotations_z.append(original_rotations_z[joint_index])
                continue

            # Get the parent index
            parent_index = reader.getJointParentIndex(joint_index)

            # Following the example implementation approach
            if joint_index == 0:  # First bone (root)
                # Get translation and rotation of the bone globally
                translation, rotation, _ = (global_matrix @ edit_bone.matrix).decompose()
            else:
                # Get translation and rotation relative to its parent
                if edit_bone.parent:
                    local_matrix = edit_bone.parent.matrix.inverted() @ edit_bone.matrix
                    translation, rotation, _ = local_matrix.decompose()
                else:
                    # If no parent, treat as global
                    translation, rotation, _ = (global_matrix @ edit_bone.matrix).decompose()

            # Keep translation in Blender units to match FBX export scale
            # The FBX is already scaled correctly, so DNA should match
            translation_blender = [
                translation.x,
                translation.y,
                translation.z
            ]

            # Convert rotation to Euler angles (in degrees)
            euler = rotation.to_euler('XYZ')
            rotation_x = math.degrees(euler.x)
            rotation_y = math.degrees(euler.y)
            rotation_z = math.degrees(euler.z)

            # Add to the lists
            joint_translations.append(translation_blender)
            joint_rotations_x.append(rotation_x)
            joint_rotations_y.append(rotation_y)
            joint_rotations_z.append(rotation_z)

        # Exit edit mode
        bpy.ops.object.mode_set(mode='OBJECT')

        # Update the joint data in the DNA
        writer.setNeutralJointTranslations(joint_translations)

        # The setNeutralJointRotations method expects a single list of rotations, not separate x, y, z lists
        # Create a list of rotations in the format expected by the DNA API
        joint_rotations = []
        for i in range(joint_count):
            joint_rotations.append([joint_rotations_x[i], joint_rotations_y[i], joint_rotations_z[i]])

        writer.setNeutralJointRotations(joint_rotations)

        log_info(f"Updated joint data for {joint_count} joints")

# Removed unused operators as part of simplification

# Classes to register
classes = [
    DNA_OT_ExportEditedDNA,
]

def register():
    """Register the DNA Editor operators"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the DNA Editor operators"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
