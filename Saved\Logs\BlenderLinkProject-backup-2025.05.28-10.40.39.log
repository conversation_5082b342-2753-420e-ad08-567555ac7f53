﻿Log file open, 05/28/25 15:39:37
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=39700)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.240586
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-9C4FBB094CF12AD9828D2AA7C9CF6E9F
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogAssetRegistry: Display: Asset registry cache read as 44.1 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.58ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.28-10.09.37:950][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.28-10.09.37:950][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.28-10.09.37:950][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.28-10.09.37:950][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.28-10.09.37:953][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.28-10.09.37:953][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.28-10.09.37:956][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-10.09.37:956][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-10.09.37:956][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-10.09.37:959][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.28-10.09.37:959][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-10.09.38:051][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.28-10.09.38:051][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-10.09.38:051][  0]LogD3D12RHI:   Adapter has 16338MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.28-10.09.38:052][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-10.09.38:052][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.28-10.09.38:220][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.28-10.09.38:220][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-10.09.38:220][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-10.09.38:220][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.28-10.09.38:220][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.28-10.09.38:229][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.28-10.09.38:229][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.28-10.09.38:229][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-10.09.38:229][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.28-10.09.38:229][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.28-10.09.38:231][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-10.09.38:231][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.28-10.09.38:231][  0]LogHAL: Display: Platform has ~ 64 GB [68632862720 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.28-10.09.38:232][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.28-10.09.38:232][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-10.09.38:232][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.28-10.09.38:232][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.28-10.09.38:232][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-10.09.38:232][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.28-10.09.38:232][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.28-10.09.38:232][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.28-10.09.38:232][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.28-10.09.38:232][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.28-10.09.38:232][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-10.09.38:232][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.28-10.09.38:232][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.28-10.09.38:232][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.28-10.09.38:232][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.28-10.09.38:232][  0]LogInit: User: Shashank
[2025.05.28-10.09.38:232][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.28-10.09.38:232][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.28-10.09.38:511][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.28-10.09.38:511][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.28-10.09.38:511][  0]LogMemory: Process Physical Memory: 630.88 MB used, 649.21 MB peak
[2025.05.28-10.09.38:511][  0]LogMemory: Process Virtual Memory: 763.43 MB used, 763.43 MB peak
[2025.05.28-10.09.38:511][  0]LogMemory: Physical Memory: 22348.91 MB used,  43104.49 MB free, 65453.40 MB total
[2025.05.28-10.09.38:511][  0]LogMemory: Virtual Memory: 38487.85 MB used,  31061.55 MB free, 69549.40 MB total
[2025.05.28-10.09.38:511][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.28-10.09.38:514][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.28-10.09.38:520][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.28-10.09.38:520][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.28-10.09.38:521][  0]LogInit: Using OS detected language (en-GB).
[2025.05.28-10.09.38:521][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.28-10.09.38:523][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.28-10.09.38:524][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.28-10.09.38:774][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.28-10.09.38:774][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.28-10.09.38:774][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.28-10.09.38:787][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.28-10.09.38:787][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.28-10.09.38:867][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-10.09.38:867][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-10.09.38:867][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-10.09.38:867][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-10.09.38:867][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-10.09.38:867][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.28-10.09.38:867][  0]LogWindows: Attached monitors:
[2025.05.28-10.09.38:867][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-10.09.38:867][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-10.09.38:867][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-10.09.38:867][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-10.09.38:867][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-10.09.38:867][  0]LogRHI: RHI Adapter Info:
[2025.05.28-10.09.38:867][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.28-10.09.38:867][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-10.09.38:867][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.28-10.09.38:867][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.28-10.09.38:891][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.28-10.09.38:952][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.28-10.09.38:952][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.28-10.09.39:020][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: Raster order views are supported
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.28-10.09.39:020][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000711ADCE5300)
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000711ADCE5580)
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000711ADCE5800)
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.28-10.09.39:046][  0]LogRHI: Texture pool is 9809 MB (70% of 14013 MB)
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.28-10.09.39:046][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.28-10.09.39:057][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.28-10.09.39:060][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.28-10.09.39:066][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.28-10.09.39:066][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.28-10.09.39:082][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.28-10.09.39:082][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.28-10.09.39:082][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.28-10.09.39:082][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.28-10.09.39:082][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.28-10.09.39:082][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.28-10.09.39:082][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.28-10.09.39:083][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.28-10.09.39:083][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.28-10.09.39:104][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.28-10.09.39:118][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.28-10.09.39:118][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.28-10.09.39:131][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.28-10.09.39:131][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.28-10.09.39:131][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.28-10.09.39:131][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.28-10.09.39:144][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.28-10.09.39:144][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.28-10.09.39:144][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.28-10.09.39:158][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.28-10.09.39:158][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.28-10.09.39:158][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.28-10.09.39:158][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.28-10.09.39:170][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.28-10.09.39:170][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.28-10.09.39:185][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.28-10.09.39:185][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.28-10.09.39:185][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.28-10.09.39:185][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.28-10.09.39:185][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.28-10.09.39:224][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.28-10.09.39:226][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.28-10.09.39:227][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.28-10.09.39:227][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.28-10.09.39:228][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.28-10.09.39:229][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.28-10.09.39:229][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.28-10.09.39:229][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-10.09.39:229][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.28-10.09.39:286][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.28-10.09.39:286][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-10.09.39:286][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.28-10.09.39:286][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.28-10.09.39:287][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-10.09.39:287][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-10.09.39:287][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.28-10.09.39:287][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 39384 --child-id Zen_39384_Startup'
[2025.05.28-10.09.39:351][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.28-10.09.39:351][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.064 seconds
[2025.05.28-10.09.39:352][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.28-10.09.39:356][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.28-10.09.39:356][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=2291.00MBs, RandomWriteSpeed=315.97MBs. Assigned SpeedClass 'Local'
[2025.05.28-10.09.39:357][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.28-10.09.39:357][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.28-10.09.39:357][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.28-10.09.39:357][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.28-10.09.39:357][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.28-10.09.39:357][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.28-10.09.39:357][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.28-10.09.39:357][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/39384/).
[2025.05.28-10.09.39:358][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/6E0D865A45FB87935CA38DAA3EDB8ACE/'.
[2025.05.28-10.09.39:358][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.28-10.09.39:358][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.28-10.09.39:359][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.28-10.09.39:359][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.28-10.09.39:799][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.28-10.09.40:296][  0]LogSlate: Using FreeType 2.10.0
[2025.05.28-10.09.40:296][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.28-10.09.40:296][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-10.09.40:296][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-10.09.40:297][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-10.09.40:297][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-10.09.40:297][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-10.09.40:297][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-10.09.40:320][  0]LogAssetRegistry: FAssetRegistry took 0.0020 seconds to start up
[2025.05.28-10.09.40:321][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.28-10.09.40:326][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.28-10.09.40:326][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.28-10.09.40:494][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.09.40:496][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.28-10.09.40:496][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.28-10.09.40:496][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.28-10.09.40:506][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.28-10.09.40:506][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.28-10.09.40:529][  0]LogDeviceProfileManager: Active device profile: [00000711CAE9CC00][00000711C8FC0000 66] WindowsEditor
[2025.05.28-10.09.40:529][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.28-10.09.40:529][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.28-10.09.40:532][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.28-10.09.40:532][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.28-10.09.40:562][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.28-10.09.40:563][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-10.09.40:564][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-10.09.40:565][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.28-10.09.40:566][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-10.09.40:567][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.28-10.09.40:567][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-10.09.40:567][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-10.09.40:567][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.28-10.09.40:567][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-10.09.40:712][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.28-10.09.40:712][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.28-10.09.40:712][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.28-10.09.40:712][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.28-10.09.40:712][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.28-10.09.40:828][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.48ms
[2025.05.28-10.09.40:845][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.48ms
[2025.05.28-10.09.40:855][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.48ms
[2025.05.28-10.09.40:857][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.48ms
[2025.05.28-10.09.41:030][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.28-10.09.41:030][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.28-10.09.41:034][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.28-10.09.41:035][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.28-10.09.41:035][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.28-10.09.41:038][  0]LogLiveCoding: Display: Waiting for server
[2025.05.28-10.09.41:050][  0]LogSlate: Border
[2025.05.28-10.09.41:050][  0]LogSlate: BreadcrumbButton
[2025.05.28-10.09.41:050][  0]LogSlate: Brushes.Title
[2025.05.28-10.09.41:050][  0]LogSlate: Default
[2025.05.28-10.09.41:050][  0]LogSlate: Icons.Save
[2025.05.28-10.09.41:050][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.28-10.09.41:050][  0]LogSlate: ListView
[2025.05.28-10.09.41:050][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.28-10.09.41:050][  0]LogSlate: SoftwareCursor_Grab
[2025.05.28-10.09.41:050][  0]LogSlate: TableView.DarkRow
[2025.05.28-10.09.41:050][  0]LogSlate: TableView.Row
[2025.05.28-10.09.41:050][  0]LogSlate: TreeView
[2025.05.28-10.09.41:103][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.28-10.09.41:110][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.28-10.09.41:112][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.790 ms
[2025.05.28-10.09.41:119][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.48ms
[2025.05.28-10.09.41:132][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.28-10.09.41:132][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.28-10.09.41:132][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.28-10.09.41:132][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.28-10.09.41:390][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.28-10.09.41:394][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.28-10.09.41:394][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.28-10.09.41:394][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:62985'.
[2025.05.28-10.09.41:396][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '230.0.0.1:6666'
[2025.05.28-10.09.41:396][  0]LogUdpMessaging: Display: Added local interface '172.27.16.1' to multicast group '230.0.0.1:6666'
[2025.05.28-10.09.41:495][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.28-10.09.41:495][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.28-10.09.41:509][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.28-10.09.41:663][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-10.09.41:663][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-10.09.41:693][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.51ms
[2025.05.28-10.09.41:788][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 39C84808F18B4E7C8000000000009E00 | Instance: 7E37237143EC4EEACD46E29D01F50366 (DESKTOP-E41IK6R-39384).
[2025.05.28-10.09.41:830][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.28-10.09.41:930][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.28-10.09.41:930][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.28-10.09.41:930][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.28-10.09.41:930][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.28-10.09.41:930][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.28-10.09.41:991][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.28-10.09.42:020][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-10.09.42:020][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-10.09.42:066][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.28-10.09.42:488][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.28-10.09.42:570][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.28-10.09.48:836][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-10.09.48:848][  0]LogSkeletalMesh: Built Skeletal Mesh [6.36s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.28-10.09.48:891][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-10.09.48:891][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-10.09.48:892][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-10.09.48:892][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-10.09.48:892][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-10.09.48:892][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-10.09.48:900][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-10.09.48:900][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-10.09.48:953][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.28-10.09.48:963][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-10.09.48:964][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-10.09.49:032][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.28-10.09.49:032][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.28-10.09.49:033][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.28-10.09.49:033][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.28-10.09.49:033][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.28-10.09.49:033][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.28-10.09.49:033][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.28-10.09.49:034][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.28-10.09.49:034][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.28-10.09.49:034][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.28-10.09.49:035][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.28-10.09.49:035][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.28-10.09.49:035][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.28-10.09.49:035][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.28-10.09.49:036][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.28-10.09.49:036][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.28-10.09.49:036][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.28-10.09.49:037][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.28-10.09.49:037][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.28-10.09.49:037][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.28-10.09.49:037][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.28-10.09.49:038][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.28-10.09.49:038][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.28-10.09.49:038][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.28-10.09.49:038][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.28-10.09.49:038][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.28-10.09.49:039][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.28-10.09.49:039][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.28-10.09.49:039][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.28-10.09.49:040][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.28-10.09.49:040][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.28-10.09.49:040][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.28-10.09.49:040][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.28-10.09.49:040][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.28-10.09.49:041][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.28-10.09.49:124][  0]SourceControl: Revision control is disabled
[2025.05.28-10.09.49:135][  0]SourceControl: Revision control is disabled
[2025.05.28-10.09.49:155][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.50ms
[2025.05.28-10.09.49:164][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.47ms
[2025.05.28-10.09.49:388][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.28-10.09.49:407][  0]LogCollectionManager: Loaded 0 collections in 0.000718 seconds
[2025.05.28-10.09.49:409][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.28-10.09.49:411][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.28-10.09.49:413][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.28-10.09.49:451][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.28-10.09.49:451][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-10.09.49:451][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.28-10.09.49:451][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.28-10.09.49:451][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.28-10.09.49:451][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.28-10.09.49:451][  0]LogBlenderLink: Waiting for client connection...
[2025.05.28-10.09.49:466][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-10.09.49:467][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-10.09.49:467][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-10.09.49:467][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-10.09.49:467][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-10.09.49:467][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-10.09.49:473][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-10.09.49:473][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-10.09.49:493][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-28T10:09:49.493Z using C
[2025.05.28-10.09.49:494][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.28-10.09.49:494][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-10.09.49:494][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.28-10.09.49:499][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.28-10.09.49:499][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.28-10.09.49:499][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.28-10.09.49:499][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000044
[2025.05.28-10.09.49:499][  0]LogFab: Display: Logging in using persist
[2025.05.28-10.09.49:499][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.28-10.09.49:527][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.28-10.09.49:527][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.28-10.09.49:538][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.28-10.09.49:538][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.28-10.09.49:650][  0]LogEngine: Initializing Engine...
[2025.05.28-10.09.49:652][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.28-10.09.49:652][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.28-10.09.49:715][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.28-10.09.49:727][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.28-10.09.49:736][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.28-10.09.49:736][  0]LogInit: Texture streaming: Enabled
[2025.05.28-10.09.49:743][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.28-10.09.49:754][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.28-10.09.49:758][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.28-10.09.49:758][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.28-10.09.49:758][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.28-10.09.49:758][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.28-10.09.49:758][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.28-10.09.49:758][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.28-10.09.49:758][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.28-10.09.49:759][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.28-10.09.49:759][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.28-10.09.49:759][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.28-10.09.49:759][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.28-10.09.49:759][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.28-10.09.49:759][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.28-10.09.49:759][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.28-10.09.49:759][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.28-10.09.49:762][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.28-10.09.49:818][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.28-10.09.49:818][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.28-10.09.49:819][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.28-10.09.49:819][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.28-10.09.49:820][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.28-10.09.49:820][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.28-10.09.49:822][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.28-10.09.49:822][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.28-10.09.49:822][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.28-10.09.49:822][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.28-10.09.49:822][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.28-10.09.49:828][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.28-10.09.49:830][  0]LogInit: Undo buffer set to 256 MB
[2025.05.28-10.09.49:830][  0]LogInit: Transaction tracking system initialized
[2025.05.28-10.09.49:840][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.28-10.09.49:878][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.56ms
[2025.05.28-10.09.49:880][  0]LocalizationService: Localization service is disabled
[2025.05.28-10.09.49:889][  0]LogTimingProfiler: Initialize
[2025.05.28-10.09.49:889][  0]LogTimingProfiler: OnSessionChanged
[2025.05.28-10.09.49:889][  0]LoadingProfiler: Initialize
[2025.05.28-10.09.49:889][  0]LoadingProfiler: OnSessionChanged
[2025.05.28-10.09.49:889][  0]LogNetworkingProfiler: Initialize
[2025.05.28-10.09.49:890][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.28-10.09.49:890][  0]LogMemoryProfiler: Initialize
[2025.05.28-10.09.49:890][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.28-10.09.50:017][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.28-10.09.50:027][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.28-10.09.50:052][  0]LogPython: Using Python 3.11.8
[2025.05.28-10.09.50:996][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.28-10.09.51:007][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.28-10.09.51:054][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.28-10.09.51:054][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.28-10.09.51:089][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.28-10.09.51:106][  0]LogEditorDataStorage: Initializing
[2025.05.28-10.09.51:107][  0]LogEditorDataStorage: Initialized
[2025.05.28-10.09.51:108][  0]LogWindows: Attached monitors:
[2025.05.28-10.09.51:108][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-10.09.51:108][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-10.09.51:108][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-10.09.51:108][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-10.09.51:108][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-10.09.51:120][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.28-10.09.51:122][  0]SourceControl: Revision control is disabled
[2025.05.28-10.09.51:122][  0]LogUnrealEdMisc: Loading editor; pre map load, took 13.902
[2025.05.28-10.09.51:122][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.28-10.09.51:123][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.09.51:123][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.09.51:126][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.28-10.09.51:166][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.28-10.09.51:168][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.55ms
[2025.05.28-10.09.51:173][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.28-10.09.51:173][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.28-10.09.51:175][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.28-10.09.51:175][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.28-10.09.51:175][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.28-10.09.51:909][  0]LogAssetRegistry: Display: Asset registry cache written as 44.1 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.28-10.09.54:081][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-10.09.54:085][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-10.09.54:086][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-10.09.54:087][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.28-10.09.54:087][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.28-10.09.54:087][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-10.09.54:089][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.28-10.09.56:066][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.28-10.09.56:111][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.28-10.09.56:485][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-10.09.56:489][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.28-10.09.56:500][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.28-10.09.56:500][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.28-10.09.56:880][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-10.09.56:882][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.28-10.09.57:950][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-10.09.57:954][  0]LogSkeletalMesh: Built Skeletal Mesh [1.45s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.28-10.09.58:048][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.28-10.09.58:258][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.28-10.09.58:264][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-10.09.58:268][  0]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.28-10.09.58:581][  0]LogWorldPartition: Display: WorldPartition initialize took 7.40 sec
[2025.05.28-10.09.58:669][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.28-10.10.03:607][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-10.10.03:621][  0]LogSkeletalMesh: Built Skeletal Mesh [5.36s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.28-10.10.04:265][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.28-10.10.04:521][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.20ms
[2025.05.28-10.10.04:522][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.28-10.10.04:524][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.505ms to complete.
[2025.05.28-10.10.04:537][  0]LogUnrealEdMisc: Total Editor Startup Time, took 27.318
[2025.05.28-10.10.04:721][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.28-10.10.04:819][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-10.10.04:883][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-10.10.04:929][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-10.10.04:979][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-10.10.05:013][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:013][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.28-10.10.05:013][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:013][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.28-10.10.05:013][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:013][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.28-10.10.05:013][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:014][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.28-10.10.05:014][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:014][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.28-10.10.05:014][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:014][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.28-10.10.05:014][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:014][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.28-10.10.05:014][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:015][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.28-10.10.05:015][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:015][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.28-10.10.05:015][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-10.10.05:015][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.28-10.10.05:055][  0]LogSlate: Took 0.000066 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.28-10.10.05:240][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.05.28-10.10.05:241][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.05.28-10.10.05:339][  0]LogSlate: Took 0.000108 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.28-10.10.05:341][  0]LogSlate: Took 0.000064 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.28-10.10.05:343][  0]LogSlate: Took 0.000064 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.28-10.10.05:376][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.28-10.10.05:378][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.28-10.10.05:378][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.28-10.10.05:378][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-10.10.05:444][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-10.10.05:444][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.28-10.10.05:445][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.28-10.10.05:445][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.28-10.10.05:445][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-10.10.05:517][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-10.10.05:517][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.28-10.10.05:551][  0]LogSlate: Took 0.000926 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.28-10.10.05:850][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 45.38 ms. Compile time 21.70 ms, link time 23.10 ms.
[2025.05.28-10.10.06:150][  0]LogStall: Startup...
[2025.05.28-10.10.06:153][  0]LogStall: Startup complete.
[2025.05.28-10.10.06:157][  0]LogLoad: (Engine Initialization) Total time: 28.94 seconds
[2025.05.28-10.10.06:375][  0]LogAssetRegistry: AssetRegistryGather time 0.0805s: AssetDataDiscovery 0.0141s, AssetDataGather 0.0109s, StoreResults 0.0554s. Wall time 26.0570s.
	NumCachedDirectories 0. NumUncachedDirectories 1886. NumCachedFiles 8092. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.28-10.10.06:396][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.28-10.10.06:396][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.28-10.10.06:514][  0]LogSourceControl: Uncontrolled asset enumeration finished in 0.117031 seconds (Found 8068 uncontrolled assets)
[2025.05.28-10.10.06:575][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.28-10.10.06:575][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.28-10.10.06:773][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.06:775][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.28-10.10.06:775][  0]LogFab: Display: Logging in using exchange code
[2025.05.28-10.10.06:775][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.28-10.10.06:775][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.28-10.10.06:775][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.28-10.10.06:804][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.28-10.10.06:814][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 39.052 ms
[2025.05.28-10.10.06:829][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.28-10.10.07:069][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.28-10.10.07:849][ 15]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 18.288818
[2025.05.28-10.10.07:850][ 15]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.28-10.10.07:850][ 15]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18.349789
[2025.05.28-10.10.08:324][ 23]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-10.10.08:899][ 34]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 19.349226
[2025.05.28-10.10.08:901][ 34]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.28-10.10.08:901][ 34]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19.349226, Update Interval: 341.910461
[2025.05.28-10.10.16:328][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.10.18:160][181]Cmd: Interchange.FeatureFlags.Import.FBX Flase
[2025.05.28-10.10.18:160][181]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.28-10.10.26:186][947]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.10.36:191][917]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.10.37:039][996]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.37:039][996]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset (0x8306E6F3EEA7F81A) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.37:039][996]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.37:114][996]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx)
[2025.05.28-10.10.37:120][996]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx
[2025.05.28-10.10.37:136][996]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.37:137][996]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.37:149][996]LogShaderCompilers: Display: Cancelled job 0x00000712D066B400 with pending SubmitJob call.
[2025.05.28-10.10.37:150][996]LogShaderCompilers: Display: Cancelled job 0x00000712D066BE00 with pending SubmitJob call.
[2025.05.28-10.10.37:151][996]LogShaderCompilers: Display: Cancelled job 0x00000712D066E600 with pending SubmitJob call.
[2025.05.28-10.10.37:151][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0668C00 with pending SubmitJob call.
[2025.05.28-10.10.37:152][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0665A00 with pending SubmitJob call.
[2025.05.28-10.10.37:152][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0660000 with pending SubmitJob call.
[2025.05.28-10.10.37:153][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0662800 with pending SubmitJob call.
[2025.05.28-10.10.37:154][996]LogShaderCompilers: Display: Cancelled job 0x00000712D04A8200 with pending SubmitJob call.
[2025.05.28-10.10.37:154][996]LogShaderCompilers: Display: Cancelled job 0x00000712D04A1E00 with pending SubmitJob call.
[2025.05.28-10.10.37:154][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0669600 with pending SubmitJob call.
[2025.05.28-10.10.37:155][996]LogShaderCompilers: Display: Cancelled job 0x00000712D04A0A00 with pending SubmitJob call.
[2025.05.28-10.10.37:155][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0666400 with pending SubmitJob call.
[2025.05.28-10.10.37:155][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0661E00 with pending SubmitJob call.
[2025.05.28-10.10.37:156][996]LogShaderCompilers: Display: Cancelled job 0x00000712D04AD200 with pending SubmitJob call.
[2025.05.28-10.10.37:156][996]LogShaderCompilers: Display: Cancelled job 0x00000712D066A000 with pending SubmitJob call.
[2025.05.28-10.10.37:157][996]LogShaderCompilers: Display: Cancelled job 0x00000712D066DC00 with pending SubmitJob call.
[2025.05.28-10.10.37:157][996]LogShaderCompilers: Display: Cancelled job 0x00000712D04A0000 with pending SubmitJob call.
[2025.05.28-10.10.37:157][996]LogShaderCompilers: Display: Cancelled job 0x00000712D04A7800 with pending SubmitJob call.
[2025.05.28-10.10.37:157][996]LogShaderCompilers: Display: Cancelled job 0x00000712D0666E00 with pending SubmitJob call.
[2025.05.28-10.10.37:157][996]LogShaderCompilers: Display: Cancelled job 0x00000712D04A9600 with pending SubmitJob call.
[2025.05.28-10.10.37:158][996]LogFbx: Triangulating skeletal mesh cartilage_lod0_mesh
[2025.05.28-10.10.37:173][996]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.37:180][996]LogSkeletalMesh: Section 0: Material=0, 576 triangles
[2025.05.28-10.10.37:181][996]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-10.10.37:187][996]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-10.10.37:188][996]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.28-10.10.37:195][996]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.37:228][996]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.37:228][996]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.37:229][996]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.28-10.10.37:236][996]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.37:236][996]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.37:236][996]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.37:322][996]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.05.28-10.10.37:356][996]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.05.28-10.10.37:372][996]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.05.28-10.10.37:387][996]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.28-10.10.37:415][996]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-10.10.37:431][996]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (cartilage_lod0_mesh) ...
[2025.05.28-10.10.37:431][996]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-10.10.37:434][996]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.37:434][996]FBXImport: Warning: The bone size is too small to create Physics Asset 'cartilage_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'cartilage_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.37:531][997]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.05.28-10.10.37:595][998]LogStreaming: Display: FlushAsyncLoading(522): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.37:595][998]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset (0x66E38450F451F47D) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.37:595][998]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.37:713][998]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx)
[2025.05.28-10.10.37:718][998]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx
[2025.05.28-10.10.37:721][998]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.37:722][998]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.37:733][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04AF000 with pending SubmitJob call.
[2025.05.28-10.10.37:734][998]LogShaderCompilers: Display: Cancelled job 0x00000712AE11A000 with pending SubmitJob call.
[2025.05.28-10.10.37:734][998]LogShaderCompilers: Display: Cancelled job 0x00000712AE118200 with pending SubmitJob call.
[2025.05.28-10.10.37:734][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04A6400 with pending SubmitJob call.
[2025.05.28-10.10.37:736][998]LogShaderCompilers: Display: Cancelled job 0x00000712AE112800 with pending SubmitJob call.
[2025.05.28-10.10.37:737][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04AE600 with pending SubmitJob call.
[2025.05.28-10.10.37:737][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04A6E00 with pending SubmitJob call.
[2025.05.28-10.10.37:737][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04A2800 with pending SubmitJob call.
[2025.05.28-10.10.37:738][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04AA000 with pending SubmitJob call.
[2025.05.28-10.10.37:738][998]LogShaderCompilers: Display: Cancelled job 0x00000712D0254600 with pending SubmitJob call.
[2025.05.28-10.10.37:738][998]LogShaderCompilers: Display: Cancelled job 0x00000712D0253200 with pending SubmitJob call.
[2025.05.28-10.10.37:738][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04A5000 with pending SubmitJob call.
[2025.05.28-10.10.37:739][998]LogShaderCompilers: Display: Cancelled job 0x0000071260349600 with pending SubmitJob call.
[2025.05.28-10.10.37:739][998]LogShaderCompilers: Display: Cancelled job 0x000007125BD01E00 with pending SubmitJob call.
[2025.05.28-10.10.37:740][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04A7800 with pending SubmitJob call.
[2025.05.28-10.10.37:740][998]LogShaderCompilers: Display: Cancelled job 0x00000712AE115000 with pending SubmitJob call.
[2025.05.28-10.10.37:740][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04A8200 with pending SubmitJob call.
[2025.05.28-10.10.37:741][998]LogShaderCompilers: Display: Cancelled job 0x000007126034D200 with pending SubmitJob call.
[2025.05.28-10.10.37:741][998]LogShaderCompilers: Display: Cancelled job 0x00000712BEFFAA00 with pending SubmitJob call.
[2025.05.28-10.10.37:741][998]LogShaderCompilers: Display: Cancelled job 0x00000712D04A0000 with pending SubmitJob call.
[2025.05.28-10.10.37:741][998]LogFbx: Triangulating skeletal mesh eyeEdge_lod0_mesh
[2025.05.28-10.10.37:750][998]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.37:755][998]LogSkeletalMesh: Section 0: Material=0, 386 triangles
[2025.05.28-10.10.37:756][998]LogSkeletalMesh: Building Skeletal Mesh eyeEdge_lod0_mesh...
[2025.05.28-10.10.37:760][998]LogSkeletalMesh: Built Skeletal Mesh [0.00s] /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh
[2025.05.28-10.10.37:761][998]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.28-10.10.37:768][998]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.37:768][998]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.37:768][998]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.37:768][998]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.28-10.10.37:775][998]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_3:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.37:775][998]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.37:775][998]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.37:791][998]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-10.10.37:823][998]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-10.10.37:838][998]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.05.28-10.10.37:854][998]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.28-10.10.37:857][998]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.37:857][998]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeEdge_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeEdge_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.37:880][999]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.05.28-10.10.37:949][  2]LogStreaming: Display: FlushAsyncLoading(528): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.37:949][  2]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset (0x3503C37BEAAD4328) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.37:949][  2]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.38:162][  2]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx)
[2025.05.28-10.10.38:166][  2]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx
[2025.05.28-10.10.38:171][  2]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.38:172][  2]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.38:233][  2]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-10.10.38:349][  2]LogFbx: Triangulating skeletal mesh eyeLeft_lod0_mesh
[2025.05.28-10.10.38:372][  2]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.38:398][  2]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-10.10.38:399][  2]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-10.10.38:416][  2]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-10.10.38:420][  2]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-10.10.38:421][  2]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.28-10.10.38:429][  2]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_4:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.38:429][  2]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.38:429][  2]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.38:430][  2]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.28-10.10.38:436][  2]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_5:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.38:436][  2]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.38:436][  2]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.38:451][  2]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-10.10.38:483][  2]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-10.10.38:499][  2]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-10.10.38:515][  2]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-10.10.38:522][  2]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-10.10.38:538][  2]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-10.10.38:553][  2]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-10.10.38:554][  2]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.38:554][  2]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeLeft_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeLeft_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.38:578][  3]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-10.10.38:703][  7]LogStreaming: Display: FlushAsyncLoading(536): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.38:703][  7]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset (0xB68C5B7FAED625F8) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.38:703][  7]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.38:910][  7]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx)
[2025.05.28-10.10.38:915][  7]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx
[2025.05.28-10.10.38:919][  7]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.38:921][  7]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.38:944][  7]LogFbxMaterialImport: Warning: Manual texture reimport and recompression may be needed for eyes_normal_map
[2025.05.28-10.10.38:982][  7]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-10.10.39:096][  7]LogFbx: Triangulating skeletal mesh eyeRight_lod0_mesh
[2025.05.28-10.10.39:120][  7]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.39:145][  7]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-10.10.39:146][  7]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-10.10.39:162][  7]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-10.10.39:168][  7]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-10.10.39:169][  7]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.28-10.10.39:177][  7]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.39:177][  7]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.39:177][  7]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.39:177][  7]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.28-10.10.39:184][  7]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_7:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.39:184][  7]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.39:184][  7]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.39:201][  7]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-10.10.39:234][  7]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-10.10.39:250][  7]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-10.10.39:265][  7]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-10.10.39:273][  7]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-10.10.39:289][  7]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-10.10.39:304][  7]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-10.10.39:305][  7]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.39:305][  7]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeRight_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeRight_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.39:329][  8]LogUObjectHash: Compacting FUObjectHashTables data took   0.85ms
[2025.05.28-10.10.39:456][ 12]LogStreaming: Display: FlushAsyncLoading(542): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.39:456][ 12]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset (0xC8AA4E7A36074A88) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.39:457][ 12]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.39:662][ 12]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx)
[2025.05.28-10.10.39:666][ 12]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx
[2025.05.28-10.10.39:669][ 12]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.39:671][ 12]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.39:684][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0658200 with pending SubmitJob call.
[2025.05.28-10.10.39:684][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712C3FDBE00 with pending SubmitJob call.
[2025.05.28-10.10.39:685][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0651400 with pending SubmitJob call.
[2025.05.28-10.10.39:685][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D065AA00 with pending SubmitJob call.
[2025.05.28-10.10.39:685][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0655A00 with pending SubmitJob call.
[2025.05.28-10.10.39:686][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712C3FD7800 with pending SubmitJob call.
[2025.05.28-10.10.39:686][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0653C00 with pending SubmitJob call.
[2025.05.28-10.10.39:686][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0657800 with pending SubmitJob call.
[2025.05.28-10.10.39:686][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0654600 with pending SubmitJob call.
[2025.05.28-10.10.39:686][ 12]LogShaderCompilers: Display: Cancelled job 0x000007126034B400 with pending SubmitJob call.
[2025.05.28-10.10.39:687][ 12]LogShaderCompilers: Display: Cancelled job 0x000007125BD01E00 with pending SubmitJob call.
[2025.05.28-10.10.39:687][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0656400 with pending SubmitJob call.
[2025.05.28-10.10.39:687][ 12]LogShaderCompilers: Display: Cancelled job 0x000007122B44AA00 with pending SubmitJob call.
[2025.05.28-10.10.39:688][ 12]LogShaderCompilers: Display: Cancelled job 0x000007122B42F000 with pending SubmitJob call.
[2025.05.28-10.10.39:688][ 12]LogFbx: Triangulating skeletal mesh eyelashes_lod0_mesh
[2025.05.28-10.10.39:689][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D025E600 with pending SubmitJob call.
[2025.05.28-10.10.39:689][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0652800 with pending SubmitJob call.
[2025.05.28-10.10.39:689][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0651E00 with pending SubmitJob call.
[2025.05.28-10.10.39:689][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712D0253200 with pending SubmitJob call.
[2025.05.28-10.10.39:690][ 12]LogShaderCompilers: Display: Cancelled job 0x00000712C3FD8200 with pending SubmitJob call.
[2025.05.28-10.10.39:690][ 12]LogShaderCompilers: Display: Cancelled job 0x000007126034BE00 with pending SubmitJob call.
[2025.05.28-10.10.39:716][ 12]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.39:732][ 12]LogSkeletalMesh: Section 0: Material=0, 1722 triangles
[2025.05.28-10.10.39:732][ 12]LogSkeletalMesh: Building Skeletal Mesh eyelashes_lod0_mesh...
[2025.05.28-10.10.39:744][ 12]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh
[2025.05.28-10.10.39:746][ 12]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.28-10.10.39:753][ 12]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_8:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.39:753][ 12]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.39:753][ 12]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.39:754][ 12]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.28-10.10.39:760][ 12]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_9:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.39:760][ 12]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.39:760][ 12]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.39:776][ 12]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-10.10.39:808][ 12]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-10.10.39:825][ 12]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.05.28-10.10.39:841][ 12]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-10.10.39:844][ 12]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.39:844][ 12]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyelashes_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyelashes_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.39:868][ 13]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-10.10.39:995][ 17]LogStreaming: Display: FlushAsyncLoading(548): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.39:995][ 17]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset (0xA8E5BC927DC4F667) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.39:995][ 17]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.40:204][ 17]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx)
[2025.05.28-10.10.40:208][ 17]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx
[2025.05.28-10.10.40:211][ 17]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.40:213][ 17]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.40:225][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0650000 with pending SubmitJob call.
[2025.05.28-10.10.40:225][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0653C00 with pending SubmitJob call.
[2025.05.28-10.10.40:225][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712C3FD8200 with pending SubmitJob call.
[2025.05.28-10.10.40:226][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712C3FD7800 with pending SubmitJob call.
[2025.05.28-10.10.40:226][ 17]LogShaderCompilers: Display: Cancelled job 0x000007125BD02800 with pending SubmitJob call.
[2025.05.28-10.10.40:226][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0656E00 with pending SubmitJob call.
[2025.05.28-10.10.40:227][ 17]LogShaderCompilers: Display: Cancelled job 0x000007122B42AA00 with pending SubmitJob call.
[2025.05.28-10.10.40:227][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0658C00 with pending SubmitJob call.
[2025.05.28-10.10.40:228][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D1C28C00 with pending SubmitJob call.
[2025.05.28-10.10.40:229][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712BEFF6400 with pending SubmitJob call.
[2025.05.28-10.10.40:229][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0650A00 with pending SubmitJob call.
[2025.05.28-10.10.40:229][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D1C2B400 with pending SubmitJob call.
[2025.05.28-10.10.40:230][ 17]LogShaderCompilers: Display: Cancelled job 0x000007122B44AA00 with pending SubmitJob call.
[2025.05.28-10.10.40:230][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0655A00 with pending SubmitJob call.
[2025.05.28-10.10.40:231][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D1C28200 with pending SubmitJob call.
[2025.05.28-10.10.40:231][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D1C26400 with pending SubmitJob call.
[2025.05.28-10.10.40:231][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0659600 with pending SubmitJob call.
[2025.05.28-10.10.40:231][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D0651400 with pending SubmitJob call.
[2025.05.28-10.10.40:231][ 17]LogShaderCompilers: Display: Cancelled job 0x00000712D065BE00 with pending SubmitJob call.
[2025.05.28-10.10.40:232][ 17]LogFbx: Triangulating skeletal mesh eyeshell_lod0_mesh
[2025.05.28-10.10.40:233][ 17]LogShaderCompilers: Display: Cancelled job 0x000007122B449600 with pending SubmitJob call.
[2025.05.28-10.10.40:250][ 17]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.40:264][ 17]LogSkeletalMesh: Section 0: Material=0, 980 triangles
[2025.05.28-10.10.40:265][ 17]LogSkeletalMesh: Building Skeletal Mesh eyeshell_lod0_mesh...
[2025.05.28-10.10.40:276][ 17]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh
[2025.05.28-10.10.40:277][ 17]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.05.28-10.10.40:285][ 17]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.40:285][ 17]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.40:285][ 17]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.40:285][ 17]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.05.28-10.10.40:292][ 17]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_11:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.40:292][ 17]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.40:292][ 17]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.40:308][ 17]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-10.10.40:341][ 17]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-10.10.40:357][ 17]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-10.10.40:373][ 17]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-10.10.40:376][ 17]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.40:376][ 17]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeshell_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeshell_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.40:399][ 18]LogUObjectHash: Compacting FUObjectHashTables data took   0.87ms
[2025.05.28-10.10.40:521][ 22]LogStreaming: Display: FlushAsyncLoading(554): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.40:522][ 22]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset (0xE7DA988AE0F17ACA) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.40:522][ 22]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.40:791][ 22]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx)
[2025.05.28-10.10.40:861][ 22]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx
[2025.05.28-10.10.44:132][ 22]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.44:135][ 22]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.44:194][ 22]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-10.10.44:309][ 22]LogFbx: Triangulating skeletal mesh head_lod0_mesh
[2025.05.28-10.10.45:675][ 22]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.45:895][ 22]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-10.10.46:380][ 22]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.28-10.10.46:446][ 22]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-10.10.46:463][ 22]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-10.10.46:510][ 22]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-10.10.47:007][ 22]LogSkeletalMesh: Built Skeletal Mesh [0.56s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-10.10.47:013][ 22]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.05.28-10.10.47:022][ 22]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_12:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.47:023][ 22]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.47:023][ 22]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.47:024][ 22]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.05.28-10.10.47:031][ 22]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.47:031][ 22]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.47:031][ 22]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.47:047][ 22]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-10.10.47:078][ 22]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-10.10.47:093][ 22]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-10.10.47:108][ 22]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.28-10.10.50:086][ 22]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-10.10.50:102][ 22]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-10.10.50:345][ 22]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-10.10.54:042][ 22]LogSkeletalMesh: Built Skeletal Mesh [3.96s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-10.10.54:237][ 22]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.54:237][ 22]FBXImport: Warning: The bone size is too small to create Physics Asset 'head_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'head_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.54:264][ 23]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-10.10.54:307][ 23]LogUObjectHash: Compacting FUObjectHashTables data took   0.91ms
[2025.05.28-10.10.54:317][ 23]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.10.54:378][ 26]LogStreaming: Display: FlushAsyncLoading(562): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.54:378][ 26]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset (0xCEBEF7E7EA79F106) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.54:378][ 26]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.54:589][ 26]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx)
[2025.05.28-10.10.54:594][ 26]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx
[2025.05.28-10.10.54:598][ 26]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.54:599][ 26]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_saliva_shader.MH_Friend_saliva_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.54:610][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0053200 with pending SubmitJob call.
[2025.05.28-10.10.54:610][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0055A00 with pending SubmitJob call.
[2025.05.28-10.10.54:611][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0057800 with pending SubmitJob call.
[2025.05.28-10.10.54:613][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0052800 with pending SubmitJob call.
[2025.05.28-10.10.54:615][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0055000 with pending SubmitJob call.
[2025.05.28-10.10.54:615][ 26]LogFbx: Triangulating skeletal mesh saliva_lod0_mesh
[2025.05.28-10.10.54:615][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B005AA00 with pending SubmitJob call.
[2025.05.28-10.10.54:616][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712D0653C00 with pending SubmitJob call.
[2025.05.28-10.10.54:616][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712C4EB6E00 with pending SubmitJob call.
[2025.05.28-10.10.54:616][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B005DC00 with pending SubmitJob call.
[2025.05.28-10.10.54:616][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B005C800 with pending SubmitJob call.
[2025.05.28-10.10.54:616][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B005F000 with pending SubmitJob call.
[2025.05.28-10.10.54:617][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B005BE00 with pending SubmitJob call.
[2025.05.28-10.10.54:617][ 26]LogShaderCompilers: Display: Cancelled job 0x0000071242EC3C00 with pending SubmitJob call.
[2025.05.28-10.10.54:617][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0058C00 with pending SubmitJob call.
[2025.05.28-10.10.54:617][ 26]LogShaderCompilers: Display: Cancelled job 0x0000071260349600 with pending SubmitJob call.
[2025.05.28-10.10.54:618][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0051400 with pending SubmitJob call.
[2025.05.28-10.10.54:619][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B005D200 with pending SubmitJob call.
[2025.05.28-10.10.54:620][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712D04A7800 with pending SubmitJob call.
[2025.05.28-10.10.54:620][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712D04A1400 with pending SubmitJob call.
[2025.05.28-10.10.54:620][ 26]LogShaderCompilers: Display: Cancelled job 0x00000712B0058200 with pending SubmitJob call.
[2025.05.28-10.10.54:635][ 26]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.54:648][ 26]LogSkeletalMesh: Section 0: Material=0, 1004 triangles
[2025.05.28-10.10.54:649][ 26]LogSkeletalMesh: Building Skeletal Mesh saliva_lod0_mesh...
[2025.05.28-10.10.54:659][ 26]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/saliva_lod0_mesh.saliva_lod0_mesh
[2025.05.28-10.10.54:660][ 26]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.05.28-10.10.54:668][ 26]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_14:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.54:668][ 26]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.54:668][ 26]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.54:668][ 26]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.05.28-10.10.54:674][ 26]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_15:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.54:674][ 26]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.54:674][ 26]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.54:691][ 26]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-10.10.54:723][ 26]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-10.10.54:740][ 26]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-10.10.54:757][ 26]LogUObjectHash: Compacting FUObjectHashTables data took   0.42ms
[2025.05.28-10.10.54:760][ 26]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.54:760][ 26]FBXImport: Warning: The bone size is too small to create Physics Asset 'saliva_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'saliva_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.54:784][ 27]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-10.10.54:862][ 30]LogStreaming: Display: FlushAsyncLoading(568): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.10.54:862][ 30]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset (0x466EF1832CA8EE25) - The package to load does not exist on disk or in the loader
[2025.05.28-10.10.54:862][ 30]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset'
[2025.05.28-10.10.55:103][ 30]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx)
[2025.05.28-10.10.55:109][ 30]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx
[2025.05.28-10.10.55:150][ 30]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-10.10.55:152][ 30]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_teeth_shader.MH_Friend_teeth_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-10.10.55:212][ 30]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-10.10.55:326][ 30]LogFbx: Triangulating skeletal mesh teeth_lod0_mesh
[2025.05.28-10.10.55:434][ 30]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-10.10.55:557][ 30]LogSkeletalMesh: Section 0: Material=0, 8350 triangles
[2025.05.28-10.10.55:559][ 30]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-10.10.55:575][ 30]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-10.10.55:661][ 30]LogSkeletalMesh: Built Skeletal Mesh [0.10s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-10.10.55:663][ 30]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.05.28-10.10.55:670][ 30]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_16:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.55:670][ 30]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.55:671][ 30]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.55:671][ 30]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.05.28-10.10.55:678][ 30]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_17:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-10.10.55:678][ 30]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.10.55:678][ 30]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.10.55:695][ 30]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-10.10.55:725][ 30]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-10.10.55:742][ 30]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-10.10.55:757][ 30]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-10.10.55:806][ 30]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-10.10.55:822][ 30]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-10.10.55:990][ 30]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-10.10.55:993][ 30]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-10.10.55:993][ 30]FBXImport: Warning: The bone size is too small to create Physics Asset 'teeth_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'teeth_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-10.10.56:016][ 31]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.05.28-10.10.56:819][ 43]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-10.10.59:426][198]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.28-10.10.59:455][199]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.05.28-10.10.59:630][202]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.05.28-10.10.59:730][203]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.05.28-10.10.59:974][207]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.05.28-10.10.59:993][208]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.05.28-10.11.00:866][262]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.05.28-10.11.00:885][263]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.05.28-10.11.00:912][264]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.05.28-10.11.00:935][265]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.05.28-10.11.01:001][269]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.05.28-10.11.01:022][270]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.05.28-10.11.01:045][271]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.05.28-10.11.01:069][272]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.05.28-10.11.01:094][273]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.05.28-10.11.01:118][274]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.05.28-10.11.01:142][275]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.05.28-10.11.01:168][276]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.05.28-10.11.01:192][277]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_35
[2025.05.28-10.11.01:217][278]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_36
[2025.05.28-10.11.03:573][349]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-10.11.03:830][349]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_37
[2025.05.28-10.11.03:847][349]LogStreaming: Display: FlushAsyncLoading(576): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-10.11.04:206][349]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_37:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-10.11.08:975][350]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.11.18:892][516]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.11.28:897][686]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.11.30:922][716]LogSlate: Window 'head_lod0_mesh' being destroyed
[2025.05.28-10.11.31:148][716]LogWorld: UWorld::CleanupWorld for World_37, bSessionEnded=true, bCleanupResources=true
[2025.05.28-10.11.31:148][716]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-10.11.31:211][716]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-10.11.38:855][387]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.11.39:357][433]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.28-10.11.48:862][287]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.11.58:867][191]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.12.08:873][101]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.12.18:873][  9]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.12.28:891][906]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.12.38:891][292]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.12.48:985][528]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.12.58:990][690]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.13.08:944][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.13.18:957][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.13.28:960][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.13.38:966][825]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.13.48:973][722]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.13.58:985][617]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.14.08:996][509]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.14.18:997][403]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.14.29:005][297]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.14.39:010][193]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.14.49:011][ 95]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.14.59:020][996]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.15.09:030][903]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.15.19:043][807]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.15.29:043][711]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.15.39:048][616]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.15.49:048][512]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.15.50:774][667]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 361.274841
[2025.05.28-10.15.51:250][709]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-10.15.51:250][709]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 361.740204, Update Interval: 316.492798
[2025.05.28-10.15.59:056][409]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.16.09:060][175]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.16.19:069][993]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.16.29:076][880]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.16.39:085][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.16.49:089][665]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.16.59:094][549]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.17.09:097][436]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.17.19:107][322]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.17.29:113][210]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.17.39:122][ 92]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.17.49:126][959]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.17.59:127][846]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.18.09:129][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.18.19:139][595]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.18.29:143][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.18.39:153][349]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.18.49:162][221]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.18.59:162][ 58]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.19.09:169][930]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.19.19:180][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.19.29:192][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.19.39:197][620]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.19.49:207][518]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.19.59:213][417]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.20.09:219][321]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.20.19:281][942]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.20.24:627][ 33]LogUObjectHash: Compacting FUObjectHashTables data took   1.51ms
[2025.05.28-10.20.24:636][ 33]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/MetaHumans/Test/TestLevel' took 0.028
[2025.05.28-10.20.24:636][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.24:723][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton.cartilage_lod0_mesh_Skeleton]
[2025.05.28-10.20.24:723][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton]
[2025.05.28-10.20.24:725][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.24:726][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/cartilage_lod0_mesh_Skeleton_Aut2EADEBFC427BA4EC66578AA23BEF30F2.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.24:726][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.24:731][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh]
[2025.05.28-10.20.24:731][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/cartilage_lod0_mesh]
[2025.05.28-10.20.24:737][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Auto1
[2025.05.28-10.20.24:738][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/cartilage_lod0_mesh_Auto11252E96249693FBDF41449942EC9D3D7.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/cartilage_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.24:738][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader] ([1] browsable assets)...
[2025.05.28-10.20.24:813][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader]
[2025.05.28-10.20.24:813][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader]
[2025.05.28-10.20.24:814][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader_Auto1
[2025.05.28-10.20.24:814][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_cartilage_shader_Auto16F60915948BC52CC93960B8923373516.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader_Auto1.uasset'
[2025.05.28-10.20.24:815][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.24:820][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton.eyeEdge_lod0_mesh_Skeleton]
[2025.05.28-10.20.24:821][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton]
[2025.05.28-10.20.24:821][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.24:821][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeEdge_lod0_mesh_Skeleton_Auto15AFBEA074E07D957BAEEE69ADBE4FEFB.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.24:822][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.24:826][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh]
[2025.05.28-10.20.24:826][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh]
[2025.05.28-10.20.24:829][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Auto1
[2025.05.28-10.20.24:829][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeEdge_lod0_mesh_Auto1D12A44B14762265867DC82827B8F67EE.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.24:830][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader] ([1] browsable assets)...
[2025.05.28-10.20.24:859][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader]
[2025.05.28-10.20.24:859][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader]
[2025.05.28-10.20.24:860][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader_Auto1
[2025.05.28-10.20.24:860][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeEdge_shader_Auto1A7283CB84BF51C5B790823BDEE2C4AD6.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader_Auto1.uasset'
[2025.05.28-10.20.24:861][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.24:866][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton.eyeLeft_lod0_mesh_Skeleton]
[2025.05.28-10.20.24:866][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton]
[2025.05.28-10.20.24:866][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.24:866][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeLeft_lod0_mesh_Skeleton_Auto13F2BC3C94DFAF07F28DD4689AB1E5247.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.24:868][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.24:872][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh]
[2025.05.28-10.20.24:872][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh]
[2025.05.28-10.20.24:877][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Auto1
[2025.05.28-10.20.24:877][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeLeft_lod0_mesh_Auto1799C5C5F41F687A9D3139A9E6C465841.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.24:878][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader] ([1] browsable assets)...
[2025.05.28-10.20.24:908][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader]
[2025.05.28-10.20.24:908][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader]
[2025.05.28-10.20.24:909][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader_Auto1
[2025.05.28-10.20.24:909][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeLeft_shader_Auto10DB886BF420D3949FA755AA90F00DA76.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader_Auto1.uasset'
[2025.05.28-10.20.24:910][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyes_normal_map] ([1] browsable assets)...
[2025.05.28-10.20.24:911][ 33]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/eyes_normal_map.eyes_normal_map]
[2025.05.28-10.20.24:911][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyes_normal_map]
[2025.05.28-10.20.25:054][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyes_normal_map_Auto1
[2025.05.28-10.20.25:054][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyes_normal_map_Auto1F0B40A9B4AE9A5A3F62853B18E5ED742.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyes_normal_map_Auto1.uasset'
[2025.05.28-10.20.25:059][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.25:101][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton.eyeRight_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:101][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:102][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.25:102][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeRight_lod0_mesh_Skeleton_Auto2F5BEE584F5D1949DDEFB0A0380D06E7.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.25:103][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.25:109][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh]
[2025.05.28-10.20.25:109][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh]
[2025.05.28-10.20.25:113][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Auto1
[2025.05.28-10.20.25:114][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeRight_lod0_mesh_Auto11A6EF20F4828B9CF7C310296D6DB589C.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.25:114][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader] ([1] browsable assets)...
[2025.05.28-10.20.25:143][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader]
[2025.05.28-10.20.25:143][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader]
[2025.05.28-10.20.25:144][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader_Auto1
[2025.05.28-10.20.25:144][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeRight_shader_Auto1BF8C251943E73DFD25A9BCA7E381F2EF.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader_Auto1.uasset'
[2025.05.28-10.20.25:145][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.25:151][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton.eyelashes_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:151][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:152][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.25:152][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyelashes_lod0_mesh_Skeleton_Aut8416F1F8497EE60DD3C8BCA7C0E989C5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.25:152][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.25:157][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh]
[2025.05.28-10.20.25:157][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh]
[2025.05.28-10.20.25:163][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Auto1
[2025.05.28-10.20.25:163][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyelashes_lod0_mesh_Auto1F79F164F46DB3E688A6555AE6BE27945.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.25:164][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader] ([1] browsable assets)...
[2025.05.28-10.20.25:193][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader]
[2025.05.28-10.20.25:193][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader]
[2025.05.28-10.20.25:194][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader_Auto1
[2025.05.28-10.20.25:194][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyelashes_shader_Auto148CCF3EE432F10A491B54BBB844A5A64.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader_Auto1.uasset'
[2025.05.28-10.20.25:195][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.25:200][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton.eyeshell_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:200][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:201][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.25:201][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeshell_lod0_mesh_Skeleton_Auto29819A5A4A3E7DACB64E6BAF35A1CB6E.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.25:202][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.25:206][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh]
[2025.05.28-10.20.25:206][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh]
[2025.05.28-10.20.25:209][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Auto1
[2025.05.28-10.20.25:210][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/eyeshell_lod0_mesh_Auto12E4AF2044429CDF244C8E985BF4757F8.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.25:210][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader] ([1] browsable assets)...
[2025.05.28-10.20.25:239][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader]
[2025.05.28-10.20.25:239][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader]
[2025.05.28-10.20.25:240][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader_Auto1
[2025.05.28-10.20.25:240][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_eyeshell_shader_Auto1F27A14FB4EFB7F8B8C300B86C194EE46.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader_Auto1.uasset'
[2025.05.28-10.20.25:241][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.25:246][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton.head_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:246][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:260][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.25:260][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_lod0_mesh_Skeleton_Auto1345266B542A14A0F4CA11591C6EE0347.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.25:261][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/head_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.25:311][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh]
[2025.05.28-10.20.25:311][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_lod0_mesh]
[2025.05.28-10.20.25:558][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Auto1
[2025.05.28-10.20.25:559][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_lod0_mesh_Auto199935C02449A8B3DA91388978C0EB344.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.25:564][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_head_shader] ([1] browsable assets)...
[2025.05.28-10.20.25:616][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader]
[2025.05.28-10.20.25:616][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_head_shader]
[2025.05.28-10.20.25:617][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_head_shader_Auto1
[2025.05.28-10.20.25:617][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_head_shader_Auto1EC6A465D41856BE91AA8ADB960673A87.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_head_shader_Auto1.uasset'
[2025.05.28-10.20.25:618][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/head_roughness_map] ([1] browsable assets)...
[2025.05.28-10.20.25:619][ 33]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/head_roughness_map.head_roughness_map]
[2025.05.28-10.20.25:619][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/head_roughness_map]
[2025.05.28-10.20.25:763][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/head_roughness_map_Auto1
[2025.05.28-10.20.25:763][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/head_roughness_map_Auto108884D2D4A7285F73FCCE09752C3AE6A.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/head_roughness_map_Auto1.uasset'
[2025.05.28-10.20.25:768][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.25:812][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton.saliva_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:812][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:813][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.25:813][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/saliva_lod0_mesh_Skeleton_Auto103ACAB244C6877E7680D1387163564B4.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.25:814][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.25:819][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/saliva_lod0_mesh.saliva_lod0_mesh]
[2025.05.28-10.20.25:819][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/saliva_lod0_mesh]
[2025.05.28-10.20.25:823][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Auto1
[2025.05.28-10.20.25:823][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/saliva_lod0_mesh_Auto16EA3C45147BF88CA88C76A9D72828D7F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/saliva_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.25:824][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader] ([1] browsable assets)...
[2025.05.28-10.20.25:853][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_saliva_shader.MH_Friend_saliva_shader]
[2025.05.28-10.20.25:853][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader]
[2025.05.28-10.20.25:855][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader_Auto1
[2025.05.28-10.20.25:855][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_saliva_shader_Auto12429011446C7789E1F7752824123B80A.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_saliva_shader_Auto1.uasset'
[2025.05.28-10.20.25:855][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton] ([1] browsable assets)...
[2025.05.28-10.20.25:860][ 33]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton.teeth_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:860][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton]
[2025.05.28-10.20.25:861][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton_Auto1
[2025.05.28-10.20.25:862][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_lod0_mesh_Skeleton_Auto11C04861444171BF3FA2CB3A141E2FAF8.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Skeleton_Auto1.uasset'
[2025.05.28-10.20.25:863][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh] ([1] browsable assets)...
[2025.05.28-10.20.25:868][ 33]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh]
[2025.05.28-10.20.25:868][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_lod0_mesh]
[2025.05.28-10.20.25:881][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Auto1
[2025.05.28-10.20.25:881][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_lod0_mesh_Auto18AC04B324D7030A88C8B5C9E8AA15E6B.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_lod0_mesh_Auto1.uasset'
[2025.05.28-10.20.25:882][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader] ([1] browsable assets)...
[2025.05.28-10.20.25:911][ 33]OBJ SavePackage:     Rendered thumbnail for [Material /Game/untitled_category/untitled_asset/MH_Friend_teeth_shader.MH_Friend_teeth_shader]
[2025.05.28-10.20.25:911][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader]
[2025.05.28-10.20.25:912][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader_Auto1
[2025.05.28-10.20.25:912][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_teeth_shader_Auto1DE6C4DDC4F295D98EBB255A2C5520576.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/MH_Friend_teeth_shader_Auto1.uasset'
[2025.05.28-10.20.25:913][ 33]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/untitled_category/untitled_asset/teeth_normal_map] ([1] browsable assets)...
[2025.05.28-10.20.25:914][ 33]OBJ SavePackage:     Rendered thumbnail for [Texture2D /Game/untitled_category/untitled_asset/teeth_normal_map.teeth_normal_map]
[2025.05.28-10.20.25:914][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/untitled_category/untitled_asset/teeth_normal_map]
[2025.05.28-10.20.26:056][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/untitled_category/untitled_asset/teeth_normal_map_Auto1
[2025.05.28-10.20.26:057][ 33]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/teeth_normal_map_Auto15815BA0F4AB8B703296F3F97AF3AE77F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/untitled_category/untitled_asset/teeth_normal_map_Auto1.uasset'
[2025.05.28-10.20.26:061][ 33]LogFileHelpers: Auto-saving content packages took 1.426
[2025.05.28-10.20.29:237][143]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.20.39:245][ 40]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.20.49:250][928]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.20.59:256][807]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.21.09:264][696]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.21.10:720][827]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 681.219788
[2025.05.28-10.21.11:009][853]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-10.21.11:009][853]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 681.498596, Update Interval: 301.684631
[2025.05.28-10.21.19:267][596]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.21.29:273][488]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.21.39:277][372]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.21.49:287][259]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.21.59:293][151]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.22.09:300][ 45]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.22.19:311][937]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.22.29:322][817]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.22.39:330][695]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.22.49:332][571]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.22.59:337][446]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.23.09:342][329]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.23.19:346][208]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.23.29:351][103]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.23.39:356][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.23.49:361][897]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.23.59:366][771]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.24.09:368][667]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.24.19:370][559]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.24.29:376][456]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.24.39:384][353]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.24.49:387][248]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.24.59:395][136]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.25.09:397][ 22]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.25.19:399][909]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.25.29:402][799]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.25.39:403][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.25.49:404][578]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.25.59:414][462]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.26.09:421][348]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.26.17:892][103]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 988.395935
[2025.05.28-10.26.18:157][127]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-10.26.18:157][127]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 988.649719, Update Interval: 345.697205
[2025.05.28-10.26.19:421][241]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.26.29:427][140]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.26.39:431][ 38]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.26.49:435][936]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.26.59:440][827]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.27.09:448][695]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.27.19:456][568]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.27.29:458][442]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.27.39:462][309]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.27.49:465][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.27.59:471][  8]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.28.09:472][856]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.28.19:482][721]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.28.29:482][580]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.28.39:487][451]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.28.49:493][323]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.28.59:498][197]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.29.09:503][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.29.19:512][953]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.29.29:518][818]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.29.39:521][682]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.29.49:533][549]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.29.59:535][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.30.09:541][278]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.30.19:544][141]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.30.29:549][ 16]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.30.39:560][868]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.30.49:565][710]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.30.59:570][549]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.31.09:573][390]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.31.19:579][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.31.29:589][ 89]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.31.39:595][932]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.31.49:596][782]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.31.59:599][647]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.32.09:603][517]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.32.12:498][770]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1342.988770
[2025.05.28-10.32.12:795][796]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-10.32.12:795][796]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1343.273804, Update Interval: 343.479736
[2025.05.28-10.32.19:611][387]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.32.29:619][257]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.32.39:622][131]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.32.49:632][  8]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.32.59:640][879]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.33.09:650][750]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.33.19:656][600]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.33.29:665][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.33.39:677][323]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.33.49:681][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.33.59:684][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.34.09:689][918]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.34.19:697][789]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.34.29:700][663]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.34.39:707][529]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.34.49:715][381]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.34.59:724][231]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.35.09:727][102]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.35.19:741][962]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.35.29:745][818]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.35.39:747][675]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.35.49:751][547]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.35.59:756][407]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.36.09:759][288]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.36.19:766][151]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.36.29:771][986]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.36.39:774][848]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.36.49:776][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.36.59:782][576]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.37.09:789][443]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.37.19:799][310]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.37.29:806][173]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.37.39:815][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.37.49:822][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.37.59:826][495]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.38.09:836][231]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.38.19:850][941]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.38.27:929][641]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1718.420410
[2025.05.28-10.38.28:192][664]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-10.38.28:192][664]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1718.671143, Update Interval: 334.152039
[2025.05.28-10.38.29:859][809]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.38.39:871][670]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.38.49:879][412]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.38.59:881][201]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.39.09:890][ 62]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.39.19:891][919]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.39.29:894][755]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.39.39:896][633]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.39.49:903][508]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.39.59:907][381]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.40.09:911][225]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.40.19:917][ 72]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.40.29:921][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-10.40.39:934][719]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
