"""
Constants for the Blender MetaHuman DNA Plugin.
"""

import os
import math
from pathlib import Path
from mathutils import Vector, Euler

# Custom bone shape scale
CUSTOM_BONE_SHAPE_NAME = "MetaHuman_BoneShape"
CUSTOM_BONE_SHAPE_SCALE = Vector((1.25, 1.25, 1.25))

# Extra bones to add to the armature
# Removed 'head', 'neck_01', 'spine_04', and 'face' to avoid conflicts with DNA bones
EXTRA_BONES = [
    ('pelvis', {
        'parent': None,  # Now pelvis is the top-level bone with no parent
        'location': Vector((0.0, 0.7957, 0.0209)),
        'rotation': E<PERSON>r((math.radians(-90.0), math.radians(-2.053), math.radians(90.0)), 'XYZ')
    }),
    ('spine_01', {
        'parent': 'pelvis',
        'location': Vector((0.0, 0.8160, 0.0206)),
        'rotation': <PERSON><PERSON><PERSON>((math.radians(-90.0), math.radians(-13.003), math.radians(90.0)), 'XYZ')
    }),
    ('spine_02', {
        'parent': 'spine_01',
        'location': Vector((0.0, 0.8576, 0.0302)),
        'rotation': Euler((math.radians(-90.0), math.radians(-5.68216), math.radians(90.0)), 'XYZ')
    }),
    ('spine_03', {
        'parent': 'spine_02',
        'location': Vector((0.0, 0.9248, 0.0369)),
        'rotation': Euler((math.radians(-90.0), math.radians(3.82404), math.radians(90.0)), 'XYZ')
    }),
    ('spine_04', {
        'parent': 'spine_03',
        'location': Vector((0.0, 1.0248, 0.0469)),
        'rotation': Euler((math.radians(-90.0), math.radians(0.0), math.radians(90.0)), 'XYZ')
    }),
]

# Bone collections
class BoneCollection:
    # Further simplified bone collections as requested - only two collections
    FACE_BONES = "Face Bones"    # All facial bones
    BODY_BONES = "Body Bones"    # All non-face bones

# Faceboard constants
FACE_BOARD_NAME = "face_gui"
RESOURCES_FOLDER = Path(os.path.dirname(__file__), "resources")
BLENDS_FOLDER = RESOURCES_FOLDER / "blends"
POSES_FOLDER = RESOURCES_FOLDER / "poses"
FACE_BOARD_FILE_PATH = BLENDS_FOLDER / "face_board.blend"
FACE_GUI_EMPTIES = [
    "GRP_C_eyesAim",
    "GRP_faceGUI",
    "LOC_C_eyeDriver",
    "head_grp",
    "headRig_grp",
    "headGui_grp",
    "headRigging_grp",
    "eyesSetup_grp"
]
