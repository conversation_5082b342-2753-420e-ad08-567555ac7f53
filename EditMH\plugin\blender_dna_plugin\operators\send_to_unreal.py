"""
Send to Unreal operator for the Blender MetaHuman DNA Plugin.
"""

import bpy
from bpy.types import Operator
from pathlib import Path

# Import our utility functions
from ..utils.output_utils import (
    update_output_items,
    update_material_slot_mappings,
    get_included_objects,
    get_lod_indexes,
    get_objects_for_lod,
    get_lod_index
)
from ..utils.dna_export_utils import export_dna_file, get_character_name_from_mesh

# Try to import the DNA modules
try:
    import dna
    DNA_MODULES_AVAILABLE = True
except ImportError:
    DNA_MODULES_AVAILABLE = False

class DNA_OT_SendToUnreal(Operator):
    """Exports the MetaHuman DNA, SkeletalMesh, and Textures, then imports them into Unreal Engine. This requires the Send to Unreal addon to be installed"""
    bl_idname = "dna.send_to_unreal"
    bl_label = "Send to Unreal"
    bl_description = "Send the MetaHuman to Unreal Engine using the Send2UE addon"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        """Check if the operator can be executed"""
        # Check if send2ue addon is available
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        if not send2ue_properties:
            return False

        # Check if DNA is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            return False

        # Check if we have a head mesh (look for head_lod0_mesh or similar)
        head_mesh_found = False
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and 'head' in obj.name.lower() and 'lod0' in obj.name.lower():
                head_mesh_found = True
                break

        if not head_mesh_found:
            return False

        return True

    def execute(self, context):
        """Execute the send to unreal operation (matching example exactly)"""
        # Check if send2ue addon is installed
        send2ue_properties = getattr(bpy.context.scene, 'send2ue', None)
        send2ue_addon_preferences = bpy.context.preferences.addons.get('send2ue')
        if not send2ue_properties:
            self.report({'ERROR'}, 'The Send to Unreal addon is not installed!')
            return {'CANCELLED'}

        dna_tools = context.scene.dna_tools

        # Get the active rig logic instance (matching example exactly)
        from ..utils.callbacks import get_active_rig_logic, get_mesh_output_items, update_output_items, update_material_slot_mappings

        # Update output items to ensure we have the latest scene state
        update_output_items()
        update_material_slot_mappings()

        instance = get_active_rig_logic()

        if not instance:
            self.report({'ERROR'}, 'No active rig logic instance found!')
            return {'CANCELLED'}

        # Check if we have required objects (matching example exactly)
        if not hasattr(instance, 'head_mesh') or not instance.head_mesh:
            self.report({'ERROR'}, 'Head mesh not found! Please ensure a MetaHuman mesh is loaded.')
            return {'CANCELLED'}

        if not hasattr(instance, 'head_rig') or not instance.head_rig:
            self.report({'ERROR'}, 'Head rig not found! Please ensure a MetaHuman armature is loaded.')
            return {'CANCELLED'}

        # Sync the spine bones with the body skeleton if enabled (matching example)
        if hasattr(instance, 'auto_sync_spine_with_body') and instance.auto_sync_spine_with_body:
            try:
                from ..utils.unreal_utils import sync_spine_with_body_skeleton
                sync_spine_with_body_skeleton(instance)
            except Exception as e:
                print(f"Warning: Could not sync spine with body: {e}")

        # Export DNA file (matching example exactly)
        try:
            from ..utils.dna_export_utils import export_dna_file
            dna_file = f'export/{instance.name}.dna'
            export_dna_file(
                dna_tools=dna_tools,
                meshes=False,
                bones=True,
                vertex_colors=False,
                file_name=dna_file
            )
            print(f"DNA file exported for Send to Unreal: {dna_file}")
        except Exception as e:
            print(f"Warning: Could not export DNA file: {e}")

        # Get included objects from output item list (matching example exactly)
        included_objects = get_mesh_output_items(instance) + [instance.head_rig]

        # Override what objects are collected by the send2ue to the head mesh (matching example exactly)
        bpy.context.window_manager.send2ue.object_collection_override.clear()
        bpy.context.window_manager.send2ue.object_collection_override.extend(included_objects)
        bpy.context.view_layer.objects.active = instance.head_mesh

        # Ensure the meta_human_dna extension is enabled so the extension logic is run (matching example exactly)
        try:
            if hasattr(bpy.context.scene.send2ue.extensions, 'meta_human_dna'):
                bpy.context.scene.send2ue.extensions.meta_human_dna.enabled = True
                print("MetaHuman DNA extension enabled for this export")
            else:
                self.report({'WARNING'}, 'MetaHuman DNA extension not found in Send2UE. Please restart Blender to activate the extension.')
                print("MetaHuman DNA extension not found - using basic export")
        except Exception as e:
            self.report({'WARNING'}, f'Could not enable MetaHuman DNA extension: {e}')
            print(f"Could not enable MetaHuman DNA extension: {e}")

        try:
            # Run send to unreal (matching example exactly)
            bpy.ops.wm.send2ue('INVOKE_DEFAULT')
            self.report({'INFO'}, "Successfully sent to Unreal Engine")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to send to Unreal Engine: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}



# Classes to register
classes = [
    DNA_OT_SendToUnreal,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)

if __name__ == "__main__":
    register()
