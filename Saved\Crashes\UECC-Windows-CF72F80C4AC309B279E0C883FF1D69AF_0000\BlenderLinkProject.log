﻿Log file open, 05/28/25 20:13:35
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=4984)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.253443
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-CF72F80C4AC309B279E0C883FF1D69AF
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading IOS ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogConfig: Display: Loading Mac ini files took 0.04 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading Unix ini files took 0.04 seconds
LogConfig: Display: Loading TVOS ini files took 0.04 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.05 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogAssetRegistry: Display: Asset registry cache read as 44.1 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.68ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.28-14.43.36:438][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.28-14.43.36:438][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.28-14.43.36:438][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.28-14.43.36:438][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.28-14.43.36:439][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.28-14.43.36:442][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.28-14.43.36:442][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.28-14.43.36:442][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.28-14.43.36:443][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.28-14.43.36:443][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.28-14.43.36:443][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.28-14.43.36:443][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.28-14.43.36:445][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-14.43.36:445][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-14.43.36:445][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-14.43.36:449][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.28-14.43.36:449][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-14.43.36:558][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.28-14.43.36:558][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-14.43.36:558][  0]LogD3D12RHI:   Adapter has 16338MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.28-14.43.36:559][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-14.43.36:559][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.28-14.43.36:713][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.28-14.43.36:713][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-14.43.36:713][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-14.43.36:713][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.28-14.43.36:713][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.28-14.43.36:721][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.28-14.43.36:721][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.28-14.43.36:721][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-14.43.36:721][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.28-14.43.36:721][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.28-14.43.36:722][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-14.43.36:722][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.28-14.43.36:722][  0]LogHAL: Display: Platform has ~ 64 GB [68632862720 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.28-14.43.36:722][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.28-14.43.36:722][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-14.43.36:722][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.28-14.43.36:722][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.28-14.43.36:722][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-14.43.36:722][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.28-14.43.36:722][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.28-14.43.36:722][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.28-14.43.36:722][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.28-14.43.36:722][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.28-14.43.36:722][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-14.43.36:722][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.28-14.43.36:722][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.28-14.43.36:722][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.28-14.43.36:722][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.28-14.43.36:723][  0]LogInit: User: Shashank
[2025.05.28-14.43.36:723][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.28-14.43.36:723][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.28-14.43.37:036][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.28-14.43.37:036][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.28-14.43.37:036][  0]LogMemory: Process Physical Memory: 627.50 MB used, 658.48 MB peak
[2025.05.28-14.43.37:036][  0]LogMemory: Process Virtual Memory: 705.21 MB used, 707.23 MB peak
[2025.05.28-14.43.37:036][  0]LogMemory: Physical Memory: 28584.89 MB used,  36868.51 MB free, 65453.40 MB total
[2025.05.28-14.43.37:036][  0]LogMemory: Virtual Memory: 35844.22 MB used,  33705.18 MB free, 69549.40 MB total
[2025.05.28-14.43.37:036][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.28-14.43.37:040][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.28-14.43.37:047][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.28-14.43.37:047][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.28-14.43.37:048][  0]LogInit: Using OS detected language (en-GB).
[2025.05.28-14.43.37:048][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.28-14.43.37:073][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.28-14.43.37:074][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.28-14.43.37:448][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.28-14.43.37:448][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.28-14.43.37:448][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.28-14.43.37:457][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.28-14.43.37:457][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.28-14.43.37:567][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-14.43.37:567][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-14.43.37:567][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-14.43.37:567][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-14.43.37:567][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-14.43.37:567][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.28-14.43.37:568][  0]LogWindows: Attached monitors:
[2025.05.28-14.43.37:568][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-14.43.37:568][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-14.43.37:568][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-14.43.37:568][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-14.43.37:568][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-14.43.37:568][  0]LogRHI: RHI Adapter Info:
[2025.05.28-14.43.37:568][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.28-14.43.37:568][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-14.43.37:568][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.28-14.43.37:568][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.28-14.43.37:593][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.28-14.43.37:658][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.28-14.43.37:659][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.28-14.43.37:738][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: Raster order views are supported
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.28-14.43.37:738][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.28-14.43.37:769][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000004A48F6F5300)
[2025.05.28-14.43.37:770][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000004A48F6F5580)
[2025.05.28-14.43.37:770][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000004A48F6F5800)
[2025.05.28-14.43.37:770][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.28-14.43.37:770][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.28-14.43.37:770][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.28-14.43.37:770][  0]LogRHI: Texture pool is 9809 MB (70% of 14013 MB)
[2025.05.28-14.43.37:770][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.28-14.43.37:770][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.28-14.43.37:782][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.28-14.43.37:821][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.28-14.43.37:829][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.28-14.43.37:829][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.28-14.43.37:848][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.28-14.43.37:848][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.28-14.43.37:848][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.28-14.43.37:848][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.28-14.43.37:848][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.28-14.43.37:848][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.28-14.43.37:848][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.28-14.43.37:848][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.28-14.43.37:848][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.28-14.43.37:875][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.28-14.43.37:889][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.28-14.43.37:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.28-14.43.37:904][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.28-14.43.37:904][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.28-14.43.37:904][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.28-14.43.37:904][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.28-14.43.37:918][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.28-14.43.37:919][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.28-14.43.37:919][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.28-14.43.37:933][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.28-14.43.37:933][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.28-14.43.37:933][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.28-14.43.37:933][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.28-14.43.37:946][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.28-14.43.37:947][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.28-14.43.37:963][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.28-14.43.37:963][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.28-14.43.37:963][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.28-14.43.37:963][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.28-14.43.37:963][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.28-14.43.38:005][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.28-14.43.38:008][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.28-14.43.38:008][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.28-14.43.38:008][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.28-14.43.38:011][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.28-14.43.38:011][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.28-14.43.38:011][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.28-14.43.38:011][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-14.43.38:011][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.28-14.43.38:090][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.28-14.43.38:090][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-14.43.38:090][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.28-14.43.38:091][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.28-14.43.38:091][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-14.43.38:091][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-14.43.38:091][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.28-14.43.38:091][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 37364 --child-id Zen_37364_Startup'
[2025.05.28-14.43.38:156][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.28-14.43.38:156][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.066 seconds
[2025.05.28-14.43.38:158][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.28-14.43.38:163][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.28-14.43.38:163][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=1999.33MBs, RandomWriteSpeed=233.30MBs. Assigned SpeedClass 'Local'
[2025.05.28-14.43.38:164][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.28-14.43.38:164][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.28-14.43.38:164][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.28-14.43.38:164][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.28-14.43.38:164][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.28-14.43.38:164][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.28-14.43.38:164][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.28-14.43.38:164][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/37364/).
[2025.05.28-14.43.38:164][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/A7FFB702468594D158444F891CC23387/'.
[2025.05.28-14.43.38:164][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.28-14.43.38:164][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.28-14.43.38:165][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.28-14.43.38:166][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.28-14.43.38:523][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.28-14.43.40:009][  0]LogSlate: Using FreeType 2.10.0
[2025.05.28-14.43.40:009][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.28-14.43.40:010][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-14.43.40:010][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-14.43.40:010][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-14.43.40:010][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-14.43.40:010][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-14.43.40:010][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-14.43.40:033][  0]LogAssetRegistry: FAssetRegistry took 0.0025 seconds to start up
[2025.05.28-14.43.40:034][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.28-14.43.40:049][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.28-14.43.40:108][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.28-14.43.40:388][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.43.40:389][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.28-14.43.40:389][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.28-14.43.40:389][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.28-14.43.40:401][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.28-14.43.40:401][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.28-14.43.40:425][  0]LogDeviceProfileManager: Active device profile: [000004A4ADE6FA00][000004A4AC040000 66] WindowsEditor
[2025.05.28-14.43.40:425][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.28-14.43.40:425][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.28-14.43.40:428][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.28-14.43.40:428][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.28-14.43.40:456][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:456][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.28-14.43.40:456][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-14.43.40:456][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:456][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.28-14.43.40:456][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-14.43.40:456][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-14.43.40:457][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:458][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.28-14.43.40:459][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-14.43.40:621][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.28-14.43.40:621][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.28-14.43.40:621][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.28-14.43.40:621][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.28-14.43.40:621][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.28-14.43.40:771][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.57ms
[2025.05.28-14.43.40:790][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.58ms
[2025.05.28-14.43.40:803][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.51ms
[2025.05.28-14.43.40:804][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.53ms
[2025.05.28-14.43.41:019][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.28-14.43.41:198][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.28-14.43.41:198][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.28-14.43.41:202][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.28-14.43.41:202][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.28-14.43.41:203][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.28-14.43.41:206][  0]LogLiveCoding: Display: Waiting for server
[2025.05.28-14.43.41:233][  0]LogSlate: Border
[2025.05.28-14.43.41:233][  0]LogSlate: BreadcrumbButton
[2025.05.28-14.43.41:233][  0]LogSlate: Brushes.Title
[2025.05.28-14.43.41:233][  0]LogSlate: Default
[2025.05.28-14.43.41:233][  0]LogSlate: Icons.Save
[2025.05.28-14.43.41:233][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.28-14.43.41:233][  0]LogSlate: ListView
[2025.05.28-14.43.41:233][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.28-14.43.41:233][  0]LogSlate: SoftwareCursor_Grab
[2025.05.28-14.43.41:233][  0]LogSlate: TableView.DarkRow
[2025.05.28-14.43.41:233][  0]LogSlate: TableView.Row
[2025.05.28-14.43.41:233][  0]LogSlate: TreeView
[2025.05.28-14.43.41:553][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.28-14.43.41:555][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.057 ms
[2025.05.28-14.43.41:588][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.49ms
[2025.05.28-14.43.41:636][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.28-14.43.41:636][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.28-14.43.41:636][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.28-14.43.41:636][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.28-14.43.42:046][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.28-14.43.43:273][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.28-14.43.43:273][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.28-14.43.43:273][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.28-14.43.43:273][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.28-14.43.43:273][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.28-14.43.43:411][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-14.43.43:411][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-14.43.43:460][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.52ms
[2025.05.28-14.43.43:613][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 6E08490891AB46E6800000000000F600 | Instance: 762EA2E349A40EA2A55C579324725198 (DESKTOP-E41IK6R-37364).
[2025.05.28-14.43.44:496][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.28-14.43.44:501][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.28-14.43.44:501][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.28-14.43.44:501][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:62650'.
[2025.05.28-14.43.44:503][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '230.0.0.1:6666'
[2025.05.28-14.43.44:503][  0]LogUdpMessaging: Display: Added local interface '172.26.192.1' to multicast group '230.0.0.1:6666'
[2025.05.28-14.43.44:657][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.28-14.43.44:658][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.28-14.43.44:673][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.28-14.43.45:247][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.28-14.43.46:094][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.28-14.43.48:257][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.28-14.43.48:324][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.28-14.43.55:300][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-14.43.55:315][  0]LogSkeletalMesh: Built Skeletal Mesh [7.06s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.28-14.43.55:565][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-14.43.55:565][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-14.43.55:566][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-14.43.55:566][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-14.43.55:566][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-14.43.55:566][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-14.43.55:907][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.28-14.43.56:334][  0]SourceControl: Revision control is disabled
[2025.05.28-14.43.56:370][  0]SourceControl: Revision control is disabled
[2025.05.28-14.43.56:449][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.53ms
[2025.05.28-14.43.56:489][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.49ms
[2025.05.28-14.43.57:602][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-14.43.57:602][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-14.43.57:693][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.28-14.43.57:733][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-14.43.57:733][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-14.43.57:999][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.28-14.43.58:000][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.28-14.43.58:000][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.28-14.43.58:000][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.28-14.43.58:000][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.28-14.43.58:000][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.28-14.43.58:001][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.28-14.43.58:002][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.28-14.43.58:002][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.28-14.43.58:002][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.28-14.43.58:003][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.28-14.43.58:003][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.28-14.43.58:003][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.28-14.43.58:004][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.28-14.43.58:004][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.28-14.43.58:005][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.28-14.43.58:005][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.28-14.43.58:006][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.28-14.43.58:006][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.28-14.43.58:006][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.28-14.43.58:006][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.28-14.43.58:006][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.28-14.43.58:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.28-14.43.58:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.28-14.43.58:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.28-14.43.58:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.28-14.43.58:008][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.28-14.43.58:008][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.28-14.43.58:008][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.28-14.43.58:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.28-14.43.58:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.28-14.43.58:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.28-14.43.58:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.28-14.43.58:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.28-14.43.58:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.28-14.43.58:174][  0]LogCollectionManager: Loaded 0 collections in 0.000727 seconds
[2025.05.28-14.43.58:177][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.28-14.43.58:179][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.28-14.43.58:181][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.28-14.43.58:231][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.28-14.43.58:231][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-14.43.58:231][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.28-14.43.58:231][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.28-14.43.58:231][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.28-14.43.58:231][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.28-14.43.58:231][  0]LogBlenderLink: Waiting for client connection...
[2025.05.28-14.43.58:248][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-14.43.58:248][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-14.43.58:249][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-14.43.58:249][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-14.43.58:249][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-14.43.58:249][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-14.43.58:262][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-14.43.58:262][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-14.43.58:278][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-28T14:43:58.278Z using C
[2025.05.28-14.43.58:279][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.28-14.43.58:279][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-14.43.58:279][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.28-14.43.58:285][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.28-14.43.58:285][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.28-14.43.58:285][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.28-14.43.58:285][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000059
[2025.05.28-14.43.58:285][  0]LogFab: Display: Logging in using persist
[2025.05.28-14.43.58:286][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.28-14.43.58:394][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.28-14.43.58:394][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.28-14.43.58:406][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.28-14.43.58:406][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.28-14.43.58:559][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/CustomizableObject.CustomizableObjectInstanceBakeOutput. Time(ms): 13.8
[2025.05.28-14.43.58:561][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/CustomizableObject.ParameterTags. Time(ms): 2.0
[2025.05.28-14.43.58:583][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/CustomizableObjectEditor.CustomizableObjectSchemaAction_NewNode. Time(ms): 21.7
[2025.05.28-14.43.58:616][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetaHumanCaptureSource.MetaHumanTakeInfo. Time(ms): 29.6
[2025.05.28-14.43.58:724][  0]LogEngine: Initializing Engine...
[2025.05.28-14.43.58:768][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.28-14.43.58:803][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.28-14.43.58:876][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.28-14.43.58:899][  0]LogTexture: Display: Waiting for textures to be ready 41/44 (/Engine/EngineMaterials/EnergyConservation/GGX_ReflectionEnergy) ...
[2025.05.28-14.43.58:907][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.28-14.43.58:936][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.28-14.43.58:936][  0]LogInit: Texture streaming: Enabled
[2025.05.28-14.43.58:944][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.28-14.43.58:955][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.28-14.43.58:959][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.28-14.43.58:960][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.28-14.43.58:961][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.28-14.43.58:961][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.28-14.43.58:961][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.28-14.43.58:961][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.28-14.43.58:961][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.28-14.43.58:961][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.28-14.43.58:961][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.28-14.43.58:961][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.28-14.43.58:961][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.28-14.43.58:961][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.28-14.43.58:961][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.28-14.43.58:961][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.28-14.43.58:961][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.28-14.43.58:965][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.28-14.43.59:024][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.28-14.43.59:024][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.28-14.43.59:025][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.28-14.43.59:025][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.28-14.43.59:026][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.28-14.43.59:026][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.28-14.43.59:029][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.28-14.43.59:029][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.28-14.43.59:029][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.28-14.43.59:029][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.28-14.43.59:029][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.28-14.43.59:055][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.28-14.43.59:058][  0]LogInit: Undo buffer set to 256 MB
[2025.05.28-14.43.59:058][  0]LogInit: Transaction tracking system initialized
[2025.05.28-14.43.59:090][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.28-14.43.59:191][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.54ms
[2025.05.28-14.43.59:192][  0]LocalizationService: Localization service is disabled
[2025.05.28-14.43.59:203][  0]LogTimingProfiler: Initialize
[2025.05.28-14.43.59:203][  0]LogTimingProfiler: OnSessionChanged
[2025.05.28-14.43.59:203][  0]LoadingProfiler: Initialize
[2025.05.28-14.43.59:203][  0]LoadingProfiler: OnSessionChanged
[2025.05.28-14.43.59:203][  0]LogNetworkingProfiler: Initialize
[2025.05.28-14.43.59:203][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.28-14.43.59:203][  0]LogMemoryProfiler: Initialize
[2025.05.28-14.43.59:203][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.28-14.43.59:382][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.28-14.43.59:393][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.28-14.43.59:522][  0]LogPython: Using Python 3.11.8
[2025.05.28-14.44.00:567][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.28-14.44.00:587][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.28-14.44.00:815][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.28-14.44.00:816][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.28-14.44.00:956][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.28-14.44.01:021][  0]LogEditorDataStorage: Initializing
[2025.05.28-14.44.01:027][  0]LogEditorDataStorage: Initialized
[2025.05.28-14.44.01:029][  0]LogWindows: Attached monitors:
[2025.05.28-14.44.01:029][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-14.44.01:029][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-14.44.01:029][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-14.44.01:029][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-14.44.01:029][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-14.44.01:072][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.28-14.44.01:074][  0]SourceControl: Revision control is disabled
[2025.05.28-14.44.01:115][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.28-14.44.01:364][  0]LogSlate: Took 0.000107 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.28-14.44.01:931][  0]LogAssetRegistry: Display: Asset registry cache written as 44.1 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.28-14.45.38:164][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.28-14.48.49:882][  0]LogSlate: Window 'Restore Packages' being destroyed
[2025.05.28-14.48.49:882][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.28-14.48.49:914][  0]LogUnrealEdMisc: Loading editor; pre map load, took 315.057
[2025.05.28-14.48.49:915][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.28-14.48.49:918][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.48.49:918][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.48.50:008][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.28-14.48.50:018][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-14.48.50:083][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.28-14.48.50:083][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.28-14.48.50:135][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.28-14.48.50:153][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.28-14.48.50:154][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.28-14.48.53:546][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-14.48.53:569][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-14.48.53:571][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-14.48.53:572][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.28-14.48.53:572][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.28-14.48.53:572][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-14.48.53:573][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.28-14.48.56:032][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.28-14.48.56:076][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.28-14.48.56:466][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-14.48.56:470][  0]LogSkeletalMesh: Built Skeletal Mesh [0.39s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.28-14.48.56:482][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.28-14.48.56:483][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.28-14.48.56:871][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-14.48.56:872][  0]LogSkeletalMesh: Built Skeletal Mesh [0.39s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.28-14.48.57:985][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-14.48.57:990][  0]LogSkeletalMesh: Built Skeletal Mesh [1.51s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.28-14.48.58:139][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.28-14.48.58:351][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.28-14.48.58:354][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-14.48.58:358][  0]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.28-14.48.58:759][  0]LogWorldPartition: Display: WorldPartition initialize took 8.60 sec
[2025.05.28-14.48.59:140][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.28-14.49.04:001][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-14.49.04:016][  0]LogSkeletalMesh: Built Skeletal Mesh [5.67s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.28-14.49.04:551][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.28-14.49.04:743][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.19ms
[2025.05.28-14.49.04:745][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.28-14.49.04:950][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 205.841ms to complete.
[2025.05.28-14.49.05:073][  0]LogUnrealEdMisc: Total Editor Startup Time, took 330.217
[2025.05.28-14.49.05:161][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.28-14.49.05:216][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.28-14.49.05:577][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-14.49.05:683][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-14.49.05:728][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-14.49.05:777][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-14.49.05:848][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:848][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.28-14.49.05:848][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:848][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.28-14.49.05:849][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:849][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.28-14.49.05:849][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:849][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.28-14.49.05:849][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:849][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.28-14.49.05:849][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:850][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.28-14.49.05:850][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:850][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.28-14.49.05:850][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:850][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.28-14.49.05:850][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:850][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.28-14.49.05:850][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-14.49.05:850][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.28-14.49.05:921][  0]LogSlate: Took 0.000119 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.28-14.49.06:175][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.05.28-14.49.06:183][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.05.28-14.49.06:541][  0]LogSlate: Took 0.000096 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.28-14.49.06:542][  0]LogSlate: Took 0.000066 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.28-14.49.06:593][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.28-14.49.06:595][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.28-14.49.06:595][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.28-14.49.06:595][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-14.49.06:670][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-14.49.06:671][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.28-14.49.06:671][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.28-14.49.06:671][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.28-14.49.06:671][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-14.49.06:737][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-14.49.06:737][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.28-14.49.06:765][  0]LogSlate: Took 0.000107 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.28-14.49.07:096][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 28.75 ms. Compile time 18.50 ms, link time 9.86 ms.
[2025.05.28-14.49.07:291][  0]LogStall: Startup...
[2025.05.28-14.49.07:294][  0]LogStall: Startup complete.
[2025.05.28-14.49.07:317][  0]LogLoad: (Engine Initialization) Total time: 332.46 seconds
[2025.05.28-14.49.07:501][  0]LogAssetRegistry: AssetRegistryGather time 0.2930s: AssetDataDiscovery 0.1336s, AssetDataGather 0.0711s, StoreResults 0.0883s. Wall time 327.4710s.
	NumCachedDirectories 0. NumUncachedDirectories 1886. NumCachedFiles 8092. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.28-14.49.07:533][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.28-14.49.07:535][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.28-14.49.07:704][  0]LogSourceControl: Uncontrolled asset enumeration finished in 0.169192 seconds (Found 8068 uncontrolled assets)
[2025.05.28-14.49.07:766][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/CX/RO749IAXISCV8LT9J5BYI5) ...
[2025.05.28-14.49.07:864][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/GK/X2E2F4C7MIW7ZEGZ962NPQ) ...
[2025.05.28-14.49.07:997][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/1/FC/6JO8A7HJKH369Q59HQCQHV) ...
[2025.05.28-14.49.08:036][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/3/Z1/78A3Y5XBCQ9FQSN66OBYNT) ...
[2025.05.28-14.49.08:091][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/2M/T8B2JWE2R8T00551G52USV) ...
[2025.05.28-14.49.08:117][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/2P/7D2L64CH3U27EU4ATOC2OJ) ...
[2025.05.28-14.49.08:142][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/7N/5XJBMH5JPWOFKB94REHVW9) ...
[2025.05.28-14.49.08:169][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/HU/3LAMRMN598G9RNI3NLQSW0) ...
[2025.05.28-14.49.08:213][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/9/7H/C8U2QI81OGWGIHLX0EPL9D) ...
[2025.05.28-14.49.08:257][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/9/W1/54GAO2N638U64KX6NWY0BJ) ...
[2025.05.28-14.49.08:290][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/A/FE/0GTAWQZWTNFD5WKTIFF37W) ...
[2025.05.28-14.49.08:311][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/A/KQ/XZI6D32LS8KYG1A6YGPPET) ...
[2025.05.28-14.49.08:437][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/C/1Y/43ZX09OBZSVBR74I50XSHI) ...
[2025.05.28-14.49.08:459][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/E/6L/1MWYH071IEHTL8SURM21XO) ...
[2025.05.28-14.49.08:573][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.28-14.49.08:573][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.28-14.49.08:707][  0]LogAutomationController: Ignoring very large delta of 16789537.78 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.49.08:707][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.49.08:709][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.28-14.49.08:709][  0]LogFab: Display: Logging in using exchange code
[2025.05.28-14.49.08:709][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.28-14.49.08:709][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.28-14.49.08:709][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.28-14.49.08:757][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.28-14.49.08:765][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 55.427 ms
[2025.05.28-14.49.08:792][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.28-14.49.09:025][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.28-14.49.09:759][ 35]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 311.462555
[2025.05.28-14.49.09:760][ 35]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.28-14.49.09:761][ 35]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 311.473724
[2025.05.28-14.49.10:046][ 63]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-14.49.10:305][ 88]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 312.011200
[2025.05.28-14.49.10:307][ 88]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.28-14.49.10:307][ 88]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 312.011200, Update Interval: 336.949982
[2025.05.28-14.49.17:327][736]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.49.27:335][685]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.49.37:340][632]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.49.47:344][581]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.49.57:346][523]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.50.07:356][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.50.17:367][388]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.50.27:376][335]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.50.37:376][282]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.50.47:378][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.50.51:253][599]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-14.50.57:386][252]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.50.57:522][266]Cmd: Interchange.FeatureFlags.Import.FBX False
[2025.05.28-14.50.57:905][266]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.28-14.51.07:395][ 10]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.51.09:021][151]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.09:028][151]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset (0x8306E6F3EEA7F81A) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.09:028][151]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.09:268][151]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx)
[2025.05.28-14.51.09:406][151]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx
[2025.05.28-14.51.09:470][151]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.09:554][151]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.09:673][151]LogFbx: Triangulating skeletal mesh cartilage_lod0_mesh
[2025.05.28-14.51.09:782][151]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.09:989][151]LogSkeletalMesh: Section 0: Material=0, 576 triangles
[2025.05.28-14.51.10:024][151]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-14.51.10:030][151]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-14.51.10:070][151]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.28-14.51.10:112][151]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.10:113][151]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.10:113][151]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.10:134][151]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.28-14.51.10:141][151]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.10:141][151]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.10:141][151]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.10:296][151]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-14.51.10:561][151]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-14.51.10:577][151]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.05.28-14.51.10:593][151]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-14.51.10:795][151]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-14.51.10:812][151]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (cartilage_lod0_mesh) ...
[2025.05.28-14.51.10:812][151]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-14.51.10:868][151]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.10:868][151]FBXImport: Warning: The bone size is too small to create Physics Asset 'cartilage_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'cartilage_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.10:992][151]LogAutomationController: Ignoring very large delta of 2.10 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.51.11:031][152]LogUObjectHash: Compacting FUObjectHashTables data took   0.94ms
[2025.05.28-14.51.11:089][152]LogStreaming: Display: FlushAsyncLoading(522): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.11:089][152]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset (0x66E38450F451F47D) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.11:089][152]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.11:122][152]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx)
[2025.05.28-14.51.11:125][152]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx
[2025.05.28-14.51.11:129][152]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.11:130][152]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.11:142][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC6400 with pending SubmitJob call.
[2025.05.28-14.51.11:142][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9202800 with pending SubmitJob call.
[2025.05.28-14.51.11:143][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9201E00 with pending SubmitJob call.
[2025.05.28-14.51.11:144][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9207800 with pending SubmitJob call.
[2025.05.28-14.51.11:144][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB3200 with pending SubmitJob call.
[2025.05.28-14.51.11:144][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB3C00 with pending SubmitJob call.
[2025.05.28-14.51.11:145][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7ECA000 with pending SubmitJob call.
[2025.05.28-14.51.11:145][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B920C800 with pending SubmitJob call.
[2025.05.28-14.51.11:145][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9206E00 with pending SubmitJob call.
[2025.05.28-14.51.11:146][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9201400 with pending SubmitJob call.
[2025.05.28-14.51.11:146][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC3200 with pending SubmitJob call.
[2025.05.28-14.51.11:147][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB1E00 with pending SubmitJob call.
[2025.05.28-14.51.11:147][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9205A00 with pending SubmitJob call.
[2025.05.28-14.51.11:147][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9204600 with pending SubmitJob call.
[2025.05.28-14.51.11:148][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC1400 with pending SubmitJob call.
[2025.05.28-14.51.11:148][152]LogFbx: Triangulating skeletal mesh eyeEdge_lod0_mesh
[2025.05.28-14.51.11:149][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB5000 with pending SubmitJob call.
[2025.05.28-14.51.11:149][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EBAA00 with pending SubmitJob call.
[2025.05.28-14.51.11:149][152]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC5000 with pending SubmitJob call.
[2025.05.28-14.51.11:149][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9208200 with pending SubmitJob call.
[2025.05.28-14.51.11:149][152]LogShaderCompilers: Display: Cancelled job 0x000004A5B9206400 with pending SubmitJob call.
[2025.05.28-14.51.11:155][152]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.11:161][152]LogSkeletalMesh: Section 0: Material=0, 386 triangles
[2025.05.28-14.51.11:162][152]LogSkeletalMesh: Building Skeletal Mesh eyeEdge_lod0_mesh...
[2025.05.28-14.51.11:166][152]LogSkeletalMesh: Built Skeletal Mesh [0.00s] /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh
[2025.05.28-14.51.11:167][152]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.28-14.51.11:176][152]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.11:176][152]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.11:176][152]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.11:176][152]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.28-14.51.11:183][152]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_3:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.11:183][152]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.11:183][152]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.11:201][152]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-14.51.11:232][152]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-14.51.11:249][152]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.05.28-14.51.11:267][152]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-14.51.11:279][152]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.11:279][152]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeEdge_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeEdge_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.11:307][153]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-14.51.11:401][159]LogStreaming: Display: FlushAsyncLoading(528): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.11:401][159]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset (0x3503C37BEAAD4328) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.11:401][159]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.11:461][159]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx)
[2025.05.28-14.51.11:465][159]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx
[2025.05.28-14.51.11:488][159]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.11:489][159]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.11:604][159]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-14.51.11:758][159]LogFbx: Triangulating skeletal mesh eyeLeft_lod0_mesh
[2025.05.28-14.51.11:780][159]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.11:805][159]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-14.51.11:806][159]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-14.51.11:822][159]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-14.51.11:828][159]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-14.51.11:829][159]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.28-14.51.11:836][159]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_4:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.11:836][159]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.11:836][159]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.11:837][159]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.28-14.51.11:844][159]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_5:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.11:844][159]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.11:844][159]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.11:863][159]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-14.51.11:895][159]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-14.51.11:912][159]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-14.51.11:929][159]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-14.51.11:937][159]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-14.51.11:953][159]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-14.51.11:968][159]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-14.51.11:968][159]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.11:968][159]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeLeft_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeLeft_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.11:994][160]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-14.51.12:036][163]LogStreaming: Display: FlushAsyncLoading(536): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.12:036][163]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset (0xB68C5B7FAED625F8) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.12:036][163]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.12:097][163]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx)
[2025.05.28-14.51.12:102][163]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx
[2025.05.28-14.51.12:106][163]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.12:107][163]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.12:138][163]LogFbxMaterialImport: Warning: Manual texture reimport and recompression may be needed for eyes_normal_map
[2025.05.28-14.51.12:196][163]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-14.51.12:328][163]LogFbx: Triangulating skeletal mesh eyeRight_lod0_mesh
[2025.05.28-14.51.12:351][163]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.12:376][163]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-14.51.12:377][163]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-14.51.12:393][163]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-14.51.12:399][163]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-14.51.12:400][163]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.28-14.51.12:408][163]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.12:434][163]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.12:434][163]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.12:435][163]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.28-14.51.12:442][163]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_7:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.12:442][163]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.12:442][163]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.12:459][163]LogUObjectHash: Compacting FUObjectHashTables data took   0.88ms
[2025.05.28-14.51.12:491][163]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.05.28-14.51.12:508][163]LogUObjectHash: Compacting FUObjectHashTables data took   0.85ms
[2025.05.28-14.51.12:524][163]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-14.51.12:531][163]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-14.51.12:547][163]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-14.51.12:568][163]LogSkeletalMesh: Built Skeletal Mesh [0.04s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-14.51.12:569][163]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.12:569][163]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeRight_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeRight_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.12:593][164]LogUObjectHash: Compacting FUObjectHashTables data took   0.91ms
[2025.05.28-14.51.12:680][171]LogStreaming: Display: FlushAsyncLoading(542): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.12:681][171]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset (0xC8AA4E7A36074A88) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.12:681][171]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.12:730][171]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx)
[2025.05.28-14.51.12:735][171]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx
[2025.05.28-14.51.12:738][171]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.12:740][171]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.12:751][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B920B400 with pending SubmitJob call.
[2025.05.28-14.51.12:751][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B920AA00 with pending SubmitJob call.
[2025.05.28-14.51.12:751][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B9208200 with pending SubmitJob call.
[2025.05.28-14.51.12:751][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB6E00 with pending SubmitJob call.
[2025.05.28-14.51.12:752][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B9206E00 with pending SubmitJob call.
[2025.05.28-14.51.12:754][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7ECE600 with pending SubmitJob call.
[2025.05.28-14.51.12:754][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B920A000 with pending SubmitJob call.
[2025.05.28-14.51.12:755][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B920C800 with pending SubmitJob call.
[2025.05.28-14.51.12:755][171]LogFbx: Triangulating skeletal mesh eyelashes_lod0_mesh
[2025.05.28-14.51.12:756][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B9201400 with pending SubmitJob call.
[2025.05.28-14.51.12:756][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB2800 with pending SubmitJob call.
[2025.05.28-14.51.12:757][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7ECBE00 with pending SubmitJob call.
[2025.05.28-14.51.12:758][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EBAA00 with pending SubmitJob call.
[2025.05.28-14.51.12:758][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC5000 with pending SubmitJob call.
[2025.05.28-14.51.12:758][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B9202800 with pending SubmitJob call.
[2025.05.28-14.51.12:758][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B9208C00 with pending SubmitJob call.
[2025.05.28-14.51.12:758][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB0000 with pending SubmitJob call.
[2025.05.28-14.51.12:759][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB5A00 with pending SubmitJob call.
[2025.05.28-14.51.12:759][171]LogShaderCompilers: Display: Cancelled job 0x000004A5B9206400 with pending SubmitJob call.
[2025.05.28-14.51.12:760][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC4600 with pending SubmitJob call.
[2025.05.28-14.51.12:760][171]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EB1400 with pending SubmitJob call.
[2025.05.28-14.51.12:783][171]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.12:798][171]LogSkeletalMesh: Section 0: Material=0, 1722 triangles
[2025.05.28-14.51.12:799][171]LogSkeletalMesh: Building Skeletal Mesh eyelashes_lod0_mesh...
[2025.05.28-14.51.12:810][171]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh
[2025.05.28-14.51.12:812][171]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.28-14.51.12:819][171]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_8:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.12:819][171]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.12:819][171]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.12:820][171]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.28-14.51.12:826][171]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_9:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.12:826][171]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.12:826][171]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.12:844][171]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-14.51.12:875][171]LogUObjectHash: Compacting FUObjectHashTables data took   0.87ms
[2025.05.28-14.51.12:893][171]LogUObjectHash: Compacting FUObjectHashTables data took   0.97ms
[2025.05.28-14.51.12:910][171]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-14.51.12:913][171]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.12:913][171]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyelashes_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyelashes_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.12:938][172]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-14.51.13:026][179]LogStreaming: Display: FlushAsyncLoading(548): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.13:026][179]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset (0xA8E5BC927DC4F667) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.13:026][179]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.13:080][179]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx)
[2025.05.28-14.51.13:085][179]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx
[2025.05.28-14.51.13:088][179]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.13:089][179]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.13:100][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC8200 with pending SubmitJob call.
[2025.05.28-14.51.13:103][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7ECDC00 with pending SubmitJob call.
[2025.05.28-14.51.13:103][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC3200 with pending SubmitJob call.
[2025.05.28-14.51.13:103][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC4600 with pending SubmitJob call.
[2025.05.28-14.51.13:103][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC6400 with pending SubmitJob call.
[2025.05.28-14.51.13:104][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7ECF000 with pending SubmitJob call.
[2025.05.28-14.51.13:105][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7EC5A00 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A5B920A000 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A5AB7E6E00 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7ECA000 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A5AB7ED200 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A5B920B400 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A5B9206400 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A5B9203C00 with pending SubmitJob call.
[2025.05.28-14.51.13:106][179]LogShaderCompilers: Display: Cancelled job 0x000004A5AB7EF000 with pending SubmitJob call.
[2025.05.28-14.51.13:107][179]LogFbx: Triangulating skeletal mesh eyeshell_lod0_mesh
[2025.05.28-14.51.13:107][179]LogShaderCompilers: Display: Cancelled job 0x000004A5B9206E00 with pending SubmitJob call.
[2025.05.28-14.51.13:108][179]LogShaderCompilers: Display: Cancelled job 0x000004A5B9201400 with pending SubmitJob call.
[2025.05.28-14.51.13:108][179]LogShaderCompilers: Display: Cancelled job 0x000004A5AB7E1400 with pending SubmitJob call.
[2025.05.28-14.51.13:108][179]LogShaderCompilers: Display: Cancelled job 0x000004A5B9204600 with pending SubmitJob call.
[2025.05.28-14.51.13:109][179]LogShaderCompilers: Display: Cancelled job 0x000004A4C7ECD200 with pending SubmitJob call.
[2025.05.28-14.51.13:124][179]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.13:137][179]LogSkeletalMesh: Section 0: Material=0, 980 triangles
[2025.05.28-14.51.13:138][179]LogSkeletalMesh: Building Skeletal Mesh eyeshell_lod0_mesh...
[2025.05.28-14.51.13:149][179]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh
[2025.05.28-14.51.13:150][179]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.05.28-14.51.13:157][179]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.13:157][179]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.13:157][179]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.13:158][179]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.05.28-14.51.13:164][179]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_11:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.13:164][179]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.13:164][179]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.13:181][179]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-14.51.13:213][179]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-14.51.13:229][179]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-14.51.13:245][179]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.05.28-14.51.13:249][179]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.13:249][179]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeshell_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeshell_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.13:273][180]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-14.51.13:361][187]LogStreaming: Display: FlushAsyncLoading(554): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.13:362][187]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset (0xE7DA988AE0F17ACA) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.13:362][187]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.13:499][187]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx)
[2025.05.28-14.51.13:570][187]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx
[2025.05.28-14.51.16:982][187]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.16:985][187]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.17:063][187]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-14.51.17:185][187]LogFbx: Triangulating skeletal mesh head_lod0_mesh
[2025.05.28-14.51.18:690][187]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.18:929][187]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-14.51.19:423][187]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.28-14.51.19:492][187]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-14.51.19:509][187]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-14.51.19:560][187]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-14.51.20:065][187]LogSkeletalMesh: Built Skeletal Mesh [0.57s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.51.20:071][187]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.05.28-14.51.20:082][187]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_12:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.20:082][187]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.20:082][187]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.20:083][187]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.05.28-14.51.20:092][187]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.20:092][187]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.20:092][187]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.20:109][187]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.05.28-14.51.20:140][187]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-14.51.20:157][187]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-14.51.20:172][187]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-14.51.23:385][187]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-14.51.23:401][187]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-14.51.23:676][187]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-14.51.27:455][187]LogSkeletalMesh: Built Skeletal Mesh [4.07s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.51.27:608][187]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.27:608][187]FBXImport: Warning: The bone size is too small to create Physics Asset 'head_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'head_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.27:613][187]LogAutomationController: Ignoring very large delta of 14.27 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.51.27:633][188]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-14.51.27:681][188]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.28-14.51.27:690][188]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.51.27:750][193]LogStreaming: Display: FlushAsyncLoading(562): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.27:751][193]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset (0xCEBEF7E7EA79F106) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.27:751][193]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.27:805][193]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx)
[2025.05.28-14.51.27:810][193]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx
[2025.05.28-14.51.27:813][193]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.27:814][193]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_saliva_shader.MH_Friend_saliva_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.27:827][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F68200 with pending SubmitJob call.
[2025.05.28-14.51.27:830][193]LogShaderCompilers: Display: Cancelled job 0x000004A5B7C60A00 with pending SubmitJob call.
[2025.05.28-14.51.27:830][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F65A00 with pending SubmitJob call.
[2025.05.28-14.51.27:830][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F67800 with pending SubmitJob call.
[2025.05.28-14.51.27:830][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F6B400 with pending SubmitJob call.
[2025.05.28-14.51.27:830][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F6A000 with pending SubmitJob call.
[2025.05.28-14.51.27:831][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F65000 with pending SubmitJob call.
[2025.05.28-14.51.27:831][193]LogShaderCompilers: Display: Cancelled job 0x000004A5796E0000 with pending SubmitJob call.
[2025.05.28-14.51.27:832][193]LogShaderCompilers: Display: Cancelled job 0x000004A5796E2800 with pending SubmitJob call.
[2025.05.28-14.51.27:832][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F61400 with pending SubmitJob call.
[2025.05.28-14.51.27:832][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F6D200 with pending SubmitJob call.
[2025.05.28-14.51.27:832][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F63C00 with pending SubmitJob call.
[2025.05.28-14.51.27:832][193]LogShaderCompilers: Display: Cancelled job 0x000004A5B9208200 with pending SubmitJob call.
[2025.05.28-14.51.27:832][193]LogShaderCompilers: Display: Cancelled job 0x000004A5796E0A00 with pending SubmitJob call.
[2025.05.28-14.51.27:834][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F6AA00 with pending SubmitJob call.
[2025.05.28-14.51.27:834][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F61E00 with pending SubmitJob call.
[2025.05.28-14.51.27:834][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F6F000 with pending SubmitJob call.
[2025.05.28-14.51.27:834][193]LogShaderCompilers: Display: Cancelled job 0x000004A5AC7C0000 with pending SubmitJob call.
[2025.05.28-14.51.27:835][193]LogShaderCompilers: Display: Cancelled job 0x000004A4BF1F4600 with pending SubmitJob call.
[2025.05.28-14.51.27:835][193]LogFbx: Triangulating skeletal mesh saliva_lod0_mesh
[2025.05.28-14.51.27:835][193]LogShaderCompilers: Display: Cancelled job 0x000004A592F6E600 with pending SubmitJob call.
[2025.05.28-14.51.27:852][193]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.27:865][193]LogSkeletalMesh: Section 0: Material=0, 1004 triangles
[2025.05.28-14.51.27:866][193]LogSkeletalMesh: Building Skeletal Mesh saliva_lod0_mesh...
[2025.05.28-14.51.27:877][193]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/saliva_lod0_mesh.saliva_lod0_mesh
[2025.05.28-14.51.27:878][193]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.05.28-14.51.27:885][193]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_14:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.27:885][193]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.27:885][193]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.27:886][193]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.05.28-14.51.27:892][193]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_15:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.27:892][193]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.27:892][193]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.27:909][193]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-14.51.27:941][193]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-14.51.27:957][193]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.28-14.51.27:974][193]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.05.28-14.51.27:977][193]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.27:977][193]FBXImport: Warning: The bone size is too small to create Physics Asset 'saliva_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'saliva_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.28:001][194]LogUObjectHash: Compacting FUObjectHashTables data took   0.76ms
[2025.05.28-14.51.28:108][202]LogStreaming: Display: FlushAsyncLoading(568): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.28:108][202]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset (0x466EF1832CA8EE25) - The package to load does not exist on disk or in the loader
[2025.05.28-14.51.28:108][202]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset'
[2025.05.28-14.51.28:164][202]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx)
[2025.05.28-14.51.28:170][202]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx
[2025.05.28-14.51.28:211][202]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-14.51.28:212][202]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_teeth_shader.MH_Friend_teeth_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-14.51.28:272][202]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-14.51.28:388][202]LogFbx: Triangulating skeletal mesh teeth_lod0_mesh
[2025.05.28-14.51.28:515][202]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-14.51.28:641][202]LogSkeletalMesh: Section 0: Material=0, 8350 triangles
[2025.05.28-14.51.28:644][202]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-14.51.28:659][202]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-14.51.28:745][202]LogSkeletalMesh: Built Skeletal Mesh [0.10s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-14.51.28:748][202]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.05.28-14.51.28:755][202]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_16:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.28:755][202]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.28:755][202]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.28:756][202]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.05.28-14.51.28:763][202]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_17:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-14.51.28:763][202]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.51.28:763][202]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.51.28:780][202]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-14.51.28:814][202]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-14.51.28:830][202]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-14.51.28:847][202]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-14.51.28:895][202]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-14.51.28:910][202]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-14.51.29:068][202]LogSkeletalMesh: Built Skeletal Mesh [0.17s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-14.51.29:070][202]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-14.51.29:070][202]FBXImport: Warning: The bone size is too small to create Physics Asset 'teeth_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'teeth_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-14.51.29:095][203]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-14.51.32:893][564]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-14.51.34:970][737]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.28-14.51.35:057][738]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.05.28-14.51.35:226][740]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.05.28-14.51.35:251][741]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.05.28-14.51.35:373][746]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.05.28-14.51.35:395][747]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.05.28-14.51.35:881][774]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.05.28-14.51.35:901][775]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.05.28-14.51.35:925][776]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.05.28-14.51.35:950][777]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.05.28-14.51.36:035][781]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.05.28-14.51.36:057][782]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.05.28-14.51.36:081][783]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.05.28-14.51.36:109][784]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.05.28-14.51.36:133][785]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.05.28-14.51.36:157][786]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.05.28-14.51.36:183][787]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.05.28-14.51.36:205][788]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.05.28-14.51.36:230][789]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_35
[2025.05.28-14.51.36:257][790]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_36
[2025.05.28-14.51.37:626][908]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.51.45:018][463]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.51.45:400][463]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_37
[2025.05.28-14.51.45:417][463]LogStreaming: Display: FlushAsyncLoading(576): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.51.45:726][463]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_37:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-14.51.50:428][463]LogAutomationController: Ignoring very large delta of 5.43 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.51.50:521][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.52.00:458][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.52.05:084][450]LogSlate: Window 'head_lod0_mesh' being destroyed
[2025.05.28-14.52.05:293][450]LogWorld: UWorld::CleanupWorld for World_37, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.52.05:293][450]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.52.05:354][450]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-14.52.10:454][927]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.52.37:191][520]LogSlate: Window 'Choose Skeleton' being destroyed
[2025.05.28-14.52.40:302][520]LogSlate: Window 'Merge Bones' being destroyed
[2025.05.28-14.52.49:706][520]LogSlate: Window 'Message' being destroyed
[2025.05.28-14.52.49:712][520]Message dialog closed, result: Yes, title: Message, text: FAILED TO MERGE BONES:  

This could happen if significant hierarchical changes have been made,
e.g. inserting a bone between nodes.
Would you like to regenerate the skeleton from this mesh? 

***WARNING: THIS WILL INVALIDATE ALL ANIMATION DATA THAT IS LINKED TO THIS SKELETON***

[2025.05.28-14.52.49:914][520]LogAnimationCompression: Display: Building compressed animation data for AnimSequence /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim (Required Memory Estimate: 5.61 MB)
[2025.05.28-14.52.49:978][520]LogAnimationCompression: Display: Storing compressed animation data for /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim, at AnimationSequence/9552a89c48e512b0ea9076bc985b040bbb4e1e51
[2025.05.28-14.52.50:062][520]LogAnimationCompression: Display: Building compressed animation data for AnimSequence /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim (Required Memory Estimate: 0.00 MB)
[2025.05.28-14.52.50:062][520]LogAnimationCompression: Display: Storing compressed animation data for /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim, at AnimationSequence/83d3ef3da1f2f76f00ddce82752b40397bb481e0
[2025.05.28-14.52.50:102][520]LogAutomationController: Ignoring very large delta of 34.51 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.52.50:144][521]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.52.50:174][523]LogRHI: Error: GpuProfiler's scratch buffer is out of space for this frame (current size : 32 kB). Dropping this frame. The size can be increased dynamically with the console variable r.GpuProfilerMaxEventBufferSizeKB
[2025.05.28-14.52.52:125][653]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.52.52:125][653]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_38
[2025.05.28-14.52.52:137][653]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_38:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-14.52.54:828][789]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/untitled_category/untitled_asset/head_lod0_mesh_Skeleton.head_lod0_mesh_Skeleton
[2025.05.28-14.52.54:842][789]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_39
[2025.05.28-14.52.54:850][789]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_39:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-14.52.55:146][789]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_40
[2025.05.28-14.52.55:404][789]LogSlate: Took 0.016622 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.05.28-14.52.59:103][979]LogWorld: UWorld::CleanupWorld for World_39, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.52.59:103][979]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.52.59:140][979]LogWorld: UWorld::CleanupWorld for World_40, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.52.59:140][979]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.52.59:196][979]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-14.53.00:118][ 26]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.53.01:005][ 87]LogSlate: Window 'head_lod0_mesh' being destroyed
[2025.05.28-14.53.01:064][ 87]LogWorld: UWorld::CleanupWorld for World_38, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.53.01:064][ 87]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.53.01:089][ 87]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-14.53.25:734][474]LogSlate: Window 'Choose Skeleton' being destroyed
[2025.05.28-14.53.25:804][474]LogAutomationController: Ignoring very large delta of 20.81 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.53.25:844][475]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.53.28:045][619]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.53.28:045][619]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_41
[2025.05.28-14.53.28:059][619]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_41:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-14.53.30:074][716]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton
[2025.05.28-14.53.30:074][716]LogStreaming: Display: FlushAsyncLoading(581): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.53.30:126][716]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.05.28-14.53.30:128][716]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.05.28-14.53.30:134][716]LogSkeletalMesh: Building Skeletal Mesh SKM_Face_Preview...
[2025.05.28-14.53.30:135][716]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_42
[2025.05.28-14.53.30:160][716]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (SKM_Face_Preview) ...
[2025.05.28-14.53.30:343][716]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-14.53.30:345][716]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview
[2025.05.28-14.53.30:347][716]LoadErrors: The Skeleton  Face_Archetype_Skeleton is missing bones that SkeletalMesh  SKM_Face_Preview needs. They will be added now. Please save the Skeleton!
[2025.05.28-14.53.30:634][716]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_42:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-14.53.30:875][716]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_43
[2025.05.28-14.53.31:106][716]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_44
[2025.05.28-14.53.35:822][979]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.53.37:896][106]LogWorld: UWorld::CleanupWorld for World_42, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.53.37:896][106]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.53.37:927][106]LogWorld: UWorld::CleanupWorld for World_43, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.53.37:927][106]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.53.37:987][106]LogUObjectHash: Compacting FUObjectHashTables data took   0.85ms
[2025.05.28-14.53.39:220][184]LogSlate: Window 'head_lod0_mesh' being destroyed
[2025.05.28-14.53.39:279][184]LogWorld: UWorld::CleanupWorld for World_41, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.53.39:279][184]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.53.39:306][184]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-14.53.40:541][294]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-14.53.44:802][599]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/MetaHumans/MH_Friend/BP_MH_Friend.BP_MH_Friend
[2025.05.28-14.53.44:823][599]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_45
[2025.05.28-14.53.45:170][599]LogStreaming: Display: FlushAsyncLoading(582): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-14.53.47:373][599]LoadErrors: The Skeleton  Face_Archetype_Skeleton is missing bones that SkeletalMesh  MH_Friend_FaceMesh needs. They will be added now. Please save the Skeleton!
[2025.05.28-14.53.47:743][599]LogSlate: Took 0.018491 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.05.28-14.53.48:330][599]LogAutomationController: Ignoring very large delta of 3.55 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.53.48:365][600]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.53.50:340][713]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-14.53.53:673][857]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_46
[2025.05.28-14.53.55:273][936]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_47
[2025.05.28-14.53.57:261][  4]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-14.53.58:362][ 53]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.54.01:709][216]LogSlate: Window 'BP_MH_Friend' being destroyed
[2025.05.28-14.54.01:802][216]LogWorld: UWorld::CleanupWorld for World_45, bSessionEnded=true, bCleanupResources=true
[2025.05.28-14.54.01:802][216]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-14.54.01:974][216]LogUObjectHash: Compacting FUObjectHashTables data took   1.32ms
[2025.05.28-14.54.02:678][254]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.28-14.54.02:679][254]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_48
[2025.05.28-14.54.02:743][254]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_48:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-14.54.05:880][254]LogAutomationController: Ignoring very large delta of 3.20 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.28-14.54.08:372][361]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.54.18:372][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.54.28:433][280]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.54.37:139][766]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.54.37:139][766]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_49
[2025.05.28-14.54.37:147][766]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_49:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.28-14.54.38:432][793]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.54.41:036][923]LogSlate: Window 'head_lod0_mesh' being destroyed
[2025.05.28-14.54.43:721][214]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_50
[2025.05.28-14.54.45:488][362]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-14.54.45:496][362]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-14.54.47:255][549]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 648.974365
[2025.05.28-14.54.47:529][580]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.28-14.54.47:529][580]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 649.238220, Update Interval: 340.725739
[2025.05.28-14.54.48:408][682]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.54.50:854][963]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-14.54.58:410][753]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-14.54.59:857][851]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.54.59:857][851]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.54.59:858][851]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_0
[2025.05.28-14.54.59:866][851]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_0
[2025.05.28-14.55.00:454][885]LogUObjectHash: Compacting FUObjectHashTables data took   1.33ms
[2025.05.28-14.55.00:468][885]LogEditorActor: Deleted 0 Actors (0.079 secs)
[2025.05.28-14.55.00:794][917]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.55.00:794][917]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.55.00:797][917]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_1
[2025.05.28-14.55.00:797][917]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_1
[2025.05.28-14.55.01:318][967]LogUObjectHash: Compacting FUObjectHashTables data took   1.35ms
[2025.05.28-14.55.01:321][967]LogEditorActor: Deleted 0 Actors (0.029 secs)
[2025.05.28-14.55.01:327][967]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.55.01:327][967]LogActorFactory: Actor Factory attempting to spawn SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-14.55.01:330][967]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_UAID_00155D088280BC6C02_2009300631
[2025.05.28-14.55.01:330][967]LogActorFactory: Actor Factory spawned SkeletalMesh /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh as actor: SkeletalMeshActor /Game/MetaHumans/Test/TestLevel.TestLevel:PersistentLevel.SkeletalMeshActor_UAID_00155D088280BC6C02_2009300631
[2025.05.28-14.55.02:746][ 47]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.05.28-14.55.02:891][ 47]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.145 s
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: === Handled ensure: ===
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: 
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: Ensure condition failed: SkeletonBoneIndex != INDEX_NONE  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\AnimGraphRuntime\Private\AnimNodes\AnimNode_LayeredBoneBlend.cpp] [Line: 77] 
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: 
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: Stack: 
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcc9690798 UnrealEditor-AnimGraphRuntime.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcc963d5dc UnrealEditor-AnimGraphRuntime.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd1ffeb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd1ffeb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcc96ce0d3 UnrealEditor-AnimGraphRuntime.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd1ffeb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd1ffeb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd12983e UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd13a66d UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd176472 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd8eb29f UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd9114eb UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd910c90 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd8ea022 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdd8ec5a2 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdcffd2b0 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffcdcf94044 UnrealEditor-Engine.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffce20fee35 UnrealEditor-Core.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffce20feb6f UnrealEditor-Core.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffce21261ad UnrealEditor-Core.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffce20eb0d6 UnrealEditor-Core.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffce22f7e23 UnrealEditor-Core.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffce274c0ad UnrealEditor-Core.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffce27435cf UnrealEditor-Core.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffe4a63259d KERNEL32.DLL!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: [Callstack] 0x00007ffe4b7aaf38 ntdll.dll!UnknownFunction []
[2025.05.28-14.55.02:892][ 47]LogOutputDevice: Error: 
