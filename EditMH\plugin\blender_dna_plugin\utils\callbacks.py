"""
Callback functions for the Blender MetaHuman DNA Plugin.

This module provides callback functions for UI elements and property updates.
"""

import os
import bpy
import json
from pathlib import Path

# Import constants
from ..constants import POSES_FOLDER

# Import logging functions
try:
    # Try the direct import first (for development)
    from .logging_utils import log_info, log_error, log_warning, log_debug
except ImportError:
    # Fall back to the full package path (for installed addon)
    from blender_metahuman_dna.blender_dna_plugin.utils.logging_utils import log_info, log_error, log_warning, log_debug

# Global variable to store preview collections
preview_collections = {}

def get_active_rig_logic():
    """
    Gets the active rig logic instance.

    Returns:
        A wrapper around DNA tools that provides the expected interface for Send2UE extension
    """
    try:
        dna_tools = bpy.context.scene.dna_tools
        if dna_tools.is_dna_loaded:
            # Create a wrapper that provides the interface expected by the extension
            class RigLogicWrapper:
                def __init__(self, dna_tools):
                    self._dna_tools = dna_tools

                @property
                def head_mesh(self):
                    return self._dna_tools.head_mesh

                @property
                def head_rig(self):
                    return self._dna_tools.head_rig

                @property
                def name(self):
                    return self._dna_tools.dna_name or 'character'

                @property
                def output_folder_path(self):
                    return self._dna_tools.output_folder_path

                @property
                def unreal_content_folder(self):
                    return self._dna_tools.unreal_content_folder

                @property
                def output_item_list(self):
                    return self._dna_tools.output_item_list

                @property
                def head_mesh_name(self):
                    if self.head_mesh:
                        return self.head_mesh.name
                    return None

                @property
                def armature_name(self):
                    if self.head_rig:
                        return self.head_rig.name
                    return None

                @property
                def faceboard_name(self):
                    # Find the faceboard armature
                    for obj in bpy.data.objects:
                        if obj.type == 'ARMATURE' and 'faceboard' in obj.name.lower():
                            return obj.name
                    return None

                @property
                def face_board(self):
                    """Get the face board object (matching example exactly)"""
                    return self._dna_tools.face_board

                @property
                def material(self):
                    """Get the material (matching example exactly)"""
                    return self._dna_tools.material

                @property
                def unreal_face_control_rig_asset_path(self):
                    """Get the unreal face control rig asset path (matching example exactly)"""
                    return self._dna_tools.unreal_face_control_rig_asset_path

                @property
                def unreal_face_anim_bp_asset_path(self):
                    """Get the unreal face anim bp asset path (matching example exactly)"""
                    return self._dna_tools.unreal_face_anim_bp_asset_path

                @property
                def unreal_blueprint_asset_path(self):
                    """Get the unreal blueprint asset path (matching example exactly)"""
                    return self._dna_tools.unreal_blueprint_asset_path

                @property
                def unreal_level_sequence_asset_path(self):
                    """Get the unreal level sequence asset path (matching example exactly)"""
                    return self._dna_tools.unreal_level_sequence_asset_path

                @property
                def unreal_copy_assets(self):
                    """Get the unreal copy assets flag (matching example exactly)"""
                    return self._dna_tools.unreal_copy_assets

                @property
                def unreal_material_slot_to_instance_mapping(self):
                    """Get the material slot to instance mapping (matching example exactly)"""
                    return self._dna_tools.unreal_material_slot_to_instance_mapping

                def evaluate(self):
                    """Evaluate the rig logic (placeholder for now)"""
                    # This would trigger rig logic evaluation
                    # For now, just print a message
                    print("Evaluating rig logic...")

            return RigLogicWrapper(dna_tools)
    except AttributeError:
        pass
    return None

def deselect_all():
    """
    Deselects all objects in the scene.
    """
    for scene_object in bpy.data.objects:
        scene_object.select_set(False)

def get_lod_index(name: str) -> int:
    """
    Gets the LOD index from the given name.

    Args:
        name (str): A name of an object.

    Returns:
        int: The LOD index. Or -1 if no LOD index was found.
    """
    import re
    # LOD regex pattern to match LOD naming conventions
    LOD_REGEX = r'.*[_\.]lod(\d+).*'
    result = re.search(LOD_REGEX, name, re.IGNORECASE)
    if result:
        lod = result.groups()[-1]
        return int(lod)
    return -1

def get_face_pose_previews_items(self, context):
    """Get the available pose thumbnails as enum items.

    This function is used as a callback for the face_pose_previews EnumProperty.
    It loads thumbnails from the poses folder and returns them as enum items.

    Args:
        self: The property owner
        context: Blender context

    Returns:
        List of enum items (identifier, name, description, icon_id, index)
    """
    enum_items = []

    if context is None:
        return enum_items

    # Get the poses folder path
    directory = POSES_FOLDER / 'face'

    # Get the preview collection
    preview_collection = preview_collections.get("face_poses")
    if not preview_collection:
        log_warning("No preview collection available")
        # Create the preview collection if it doesn't exist
        import bpy.utils.previews
        preview_collection = bpy.utils.previews.new()
        preview_collections["face_poses"] = preview_collection
        log_info("Created new preview collection for face poses")

    # If the enum items have already been cached, return them
    if hasattr(preview_collection, 'face_pose_previews') and preview_collection.face_pose_previews:
        return preview_collection.face_pose_previews

    if directory.exists():
        image_paths = []

        # Find all thumbnail-preview.png files that have a corresponding pose.json
        for folder_path, _, file_names in os.walk(directory):
            for file_name in file_names:
                if file_name == 'thumbnail-preview.png':
                    thumbnail_file_path = Path(folder_path, file_name)
                    pose_file_path = Path(folder_path, 'pose.json')
                    if pose_file_path.exists() and thumbnail_file_path.exists():
                        image_paths.append(Path(folder_path, file_name))

        # Create enum items for each thumbnail
        for i, file_path in enumerate(image_paths):
            name = file_path.parent.name
            # Generate a thumbnail preview for the file
            icon = preview_collection.get(name)
            if not icon:
                try:
                    thumb = preview_collection.load(name, str(file_path), 'IMAGE')
                    log_info(f"Loaded thumbnail for {name}: {file_path}")
                except Exception as e:
                    log_error(f"Error loading thumbnail for {name}: {e}")
                    # Create a default icon if loading fails
                    try:
                        # Use a default icon from Blender
                        enum_items.append((str(file_path), name, "", 0, i))
                        log_info(f"Using default icon for {name}")
                        continue
                    except Exception as e2:
                        log_error(f"Error creating default icon for {name}: {e2}")
                        continue
            else:
                thumb = preview_collection[name]

            # Add the enum item with the thumbnail
            enum_items.append((str(file_path), name, "", thumb.icon_id, i))
            log_info(f"Added enum item for {name} with icon ID {thumb.icon_id}")

        # Sort items alphabetically by name
        enum_items.sort(key=lambda x: x[1])

        # Log the number of enum items
        log_info(f"Created {len(enum_items)} enum items for face pose previews")

    # Cache the enum items
    preview_collection.face_pose_previews = enum_items

    return enum_items

def update_face_pose(self, context):
    """Update the UI when a pose is selected.

    This function is called when the face_pose_previews property is changed.
    It only updates the UI to show the selected pose, but does not apply the pose
    or send any packets. The pose will be applied when the user clicks the "Apply Selected Pose" button.

    Args:
        self: The property owner
        context: Blender context
    """
    # Get the selected pose file path
    thumbnail_file = Path(self.face_pose_previews)
    json_file_path = thumbnail_file.parent / 'pose.json'

    if not json_file_path.exists():
        log_warning(f"Pose file not found: {json_file_path}")
        return

    # Just log that a pose was selected, but don't apply it yet
    pose_name = thumbnail_file.parent.name
    log_info(f"Selected pose: {pose_name} (will be applied when 'Apply Selected Pose' is clicked)")

    # Store the selected pose path for later use
    # This is just a UI update, no packets are sent
    context.window_manager.dna_tools.selected_pose_path = str(json_file_path)

def get_mesh_output_items(instance):
    """
    Get all mesh objects that are skinned to the head rig (matching example exactly).

    :param instance: The rig logic instance
    :return list: List of mesh objects
    """
    mesh_objects = []

    # Get all mesh objects that are skinned to the head rig
    if hasattr(instance, 'head_rig') and instance.head_rig:
        for scene_object in bpy.data.objects:
            if scene_object.type == 'MESH':
                for modifier in scene_object.modifiers:
                    if modifier.type == 'ARMATURE' and modifier.object == instance.head_rig:
                        mesh_objects.append(scene_object)
                        break

    return mesh_objects


def get_included_objects():
    """
    Get the list of included objects from the output item list.

    :return list: List of included objects
    """
    included_objects = []

    try:
        dna_tools = bpy.context.scene.dna_tools

        # Get objects from output item list
        for item in dna_tools.output_item_list:
            if item.include and item.scene_object:
                included_objects.append(item.scene_object)

    except AttributeError:
        pass

    return included_objects


def update_output_items():
    """
    Update the output item list based on current scene objects (matching example exactly).
    """
    try:
        dna_tools = bpy.context.scene.dna_tools

        # Clear existing output items
        dna_tools.output_item_list.clear()

        # Find and set head mesh reference
        if not dna_tools.head_mesh:
            for obj in bpy.data.objects:
                if obj.type == 'MESH' and 'head' in obj.name.lower() and 'lod0' in obj.name.lower():
                    dna_tools.head_mesh = obj
                    break

        # Find and set head rig reference
        if not dna_tools.head_rig:
            for obj in bpy.data.objects:
                if obj.type == 'ARMATURE' and obj.name == 'root':
                    dna_tools.head_rig = obj
                    break

        # Add mesh objects to output list
        if hasattr(dna_tools, 'head_rig') and dna_tools.head_rig:
            instance = get_active_rig_logic()
            if instance:
                mesh_objects = get_mesh_output_items(instance) + [instance.head_rig]

                for mesh_object in mesh_objects:
                    # Check if item already exists
                    existing_item = None
                    for item in dna_tools.output_item_list:
                        if item.scene_object == mesh_object:
                            existing_item = item
                            break

                    if not existing_item:
                        new_item = dna_tools.output_item_list.add()
                        new_item.scene_object = mesh_object

                        # Set appropriate names (matching example)
                        if mesh_object == dna_tools.head_mesh:
                            new_item.name = 'head_lod0_mesh'
                            new_item.editable_name = False
                        elif mesh_object == dna_tools.head_rig:
                            new_item.name = 'rig'
                            new_item.editable_name = False
                        else:
                            # Remove instance name prefix if it exists
                            instance_name = getattr(dna_tools, 'name', 'my_metahuman')
                            new_item.name = mesh_object.name.replace(f'{instance_name}_', '')
                            new_item.editable_name = True

                        new_item.include = True

    except AttributeError as e:
        print(f"Error updating output items: {e}")


def update_material_slot_mappings():
    """
    Update the material slot mappings based on current materials.
    """
    try:
        dna_tools = bpy.context.scene.dna_tools

        # Clear existing mappings
        dna_tools.material_slot_mappings.clear()

        # Add material slots from head mesh
        if hasattr(dna_tools, 'head_mesh') and dna_tools.head_mesh:
            for i, material_slot in enumerate(dna_tools.head_mesh.material_slots):
                if material_slot.material:
                    new_mapping = dna_tools.material_slot_mappings.add()
                    new_mapping.name = material_slot.material.name
                    new_mapping.asset_path = f"/Game/MetaHumans/Common/Materials/{material_slot.material.name}"
                    new_mapping.valid_path = True

    except AttributeError as e:
        print(f"Error updating material slot mappings: {e}")


def export_file(properties, lod=0):
    """Export file using Send2UE (real implementation)"""
    try:
        # Import Send2UE export functionality
        from send2ue.core.export import export_file as send2ue_export_file
        from send2ue.constants import FileTypes

        # Call the actual Send2UE export function
        send2ue_export_file(properties, lod=lod, file_type=FileTypes.FBX)
        print(f"Successfully exported LOD {lod} file using Send2UE")

    except ImportError as e:
        print(f"Could not import Send2UE export functionality: {e}")
        print(f"Falling back to placeholder for LOD {lod} export")
    except Exception as e:
        print(f"Error exporting LOD {lod} file: {e}")

def make_remote(func):
    """Make a function remote using Send2UE RPC (real implementation)"""
    try:
        # Import Send2UE RPC factory
        from send2ue.dependencies.rpc.factory import make_remote as send2ue_make_remote

        # Use the actual Send2UE make_remote function
        return send2ue_make_remote(func)

    except ImportError as e:
        print(f"Could not import Send2UE RPC functionality: {e}")
        print("Falling back to placeholder remote function")

        # Fallback to placeholder implementation
        def wrapper(*args, **kwargs):
            try:
                print(f"Remote call to {func.__name__} with args: {args}")
                # In a full implementation, this would make an RPC call to Unreal
                # For now, we'll just call the function locally
                return func(*args, **kwargs)
            except Exception as e:
                print(f"Error in remote call: {e}")
                return None
        return wrapper
    except Exception as e:
        print(f"Error setting up remote function: {e}")
        return func

# Register and unregister functions
def register():
    """Register the callbacks module"""
    global preview_collections

    # Create the preview collection for face poses
    import bpy.utils.previews
    face_pose_previews_collection = bpy.utils.previews.new()
    face_pose_previews_collection.face_pose_previews_root_folder = ""
    face_pose_previews_collection.face_pose_previews = []
    preview_collections["face_poses"] = face_pose_previews_collection

    # Load thumbnails for all poses
    try:
        # Get the poses folder path
        directory = POSES_FOLDER / 'face'
        if directory.exists():
            log_info(f"Loading thumbnails from {directory}")

            # Find all thumbnail-preview.png files
            image_paths = []
            for folder_path, _, file_names in os.walk(directory):
                for file_name in file_names:
                    if file_name == 'thumbnail-preview.png':
                        thumbnail_file_path = Path(folder_path, file_name)
                        pose_file_path = Path(folder_path, 'pose.json')
                        if pose_file_path.exists() and thumbnail_file_path.exists():
                            image_paths.append(Path(folder_path, file_name))

            log_info(f"Found {len(image_paths)} thumbnails")

            # Load thumbnails into the preview collection
            for file_path in image_paths:
                name = file_path.parent.name
                try:
                    face_pose_previews_collection.load(name, str(file_path), 'IMAGE')
                    log_info(f"Loaded thumbnail for {name}: {file_path}")
                except Exception as e:
                    log_error(f"Error loading thumbnail for {name}: {e}")
    except Exception as e:
        log_error(f"Error loading thumbnails: {e}")

    log_info("Registered callbacks module")

def unregister():
    """Unregister the callbacks module"""
    global preview_collections

    # Remove preview collections
    for pcoll in preview_collections.values():
        bpy.utils.previews.remove(pcoll)
    preview_collections.clear()

    log_info("Unregistered callbacks module")
