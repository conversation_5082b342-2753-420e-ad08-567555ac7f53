"""
Unreal Engine utilities for MetaHuman DNA plugin.
Provides functions for interacting with Unreal Engine via Send2UE.
"""

import bpy
from pathlib import Path
import sys
import os
from ..utils.misc import preserve_context, send2ue_addon_is_valid


@preserve_context
def sync_spine_with_body_skeleton(instance):
    """
    Sync the spine bones with the body skeleton in the Unreal blueprint.
    This matches the implementation from @EditMH\example\.
    
    Args:
        instance: The rig logic instance containing the blueprint path
    """
    if instance.unreal_blueprint_asset_path and send2ue_addon_is_valid():
        try:
            # Import Send2UE RPC functionality
            from send2ue.dependencies.rpc.factory import make_remote
            from send2ue.dependencies.unreal import bootstrap_unreal_with_rpc_server

            # Import our meta_human_dna_utilities
            plugin_path = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            unreal_utils_path = os.path.join(plugin_path, 'resources', 'unreal')
            if unreal_utils_path not in sys.path:
                sys.path.append(unreal_utils_path)
            
            from meta_human_dna_utilities import get_body_bone_transforms

            # Bootstrap Unreal RPC server
            bootstrap_unreal_with_rpc_server()
            
            # Get bone transforms from Unreal blueprint
            remote_get_body_bone_transforms = make_remote(get_body_bone_transforms)
            bone_transforms = remote_get_body_bone_transforms(str(instance.unreal_blueprint_asset_path))
            
            # Apply transforms to head rig
            instance.head_rig.hide_set(False)
            switch_to_pose_mode(instance.head_rig)

            # Deselect all bones
            for bone in instance.head_rig.data.bones:
                bone.select = False
                bone.select_head = False
                bone.select_tail = False

            # Apply bone transforms
            if bone_transforms:
                for bone_name, transform in bone_transforms.items():
                    if bone_name in instance.head_rig.pose.bones:
                        pose_bone = instance.head_rig.pose.bones[bone_name]
                        # Apply the transform (this would need proper matrix conversion)
                        # For now, we'll just print what would be applied
                        print(f"Would apply transform to bone {bone_name}: {transform}")
                        
            print(f"Synced spine bones with body skeleton from: {instance.unreal_blueprint_asset_path}")
            
        except ImportError as e:
            print(f"Could not import Send2UE RPC functionality: {e}")
        except Exception as e:
            print(f"Error syncing spine with body skeleton: {e}")
    else:
        print("Blueprint asset path not set or Send2UE addon not available")


def switch_to_pose_mode(armature_object):
    """
    Switch to pose mode for the given armature object.
    
    Args:
        armature_object: The armature object to switch to pose mode
    """
    if armature_object and armature_object.type == 'ARMATURE':
        # Set the armature as active
        bpy.context.view_layer.objects.active = armature_object
        
        # Switch to pose mode
        if bpy.context.mode != 'POSE':
            bpy.ops.object.mode_set(mode='POSE')


def get_body_bone_transforms(blueprint_asset_path):
    """
    Placeholder function for getting body bone transforms from Unreal.
    This would be implemented in the meta_human_dna_utilities module.
    
    Args:
        blueprint_asset_path (str): Path to the Unreal blueprint asset
        
    Returns:
        dict: Dictionary of bone names to transform data
    """
    # This is a placeholder - the actual implementation would be in 
    # meta_human_dna_utilities and would query Unreal Engine
    print(f"Getting body bone transforms from: {blueprint_asset_path}")
    return {}
